// 超简单主题切换器 - 点击就换
document.addEventListener('DOMContentLoaded', function() {
    console.log('主题切换器加载完成');

    // 加载保存的主题
    const savedTheme = localStorage.getItem('user-theme') || 'primary';
    document.documentElement.setAttribute('data-theme', savedTheme);
    document.body.setAttribute('data-theme', savedTheme);
    console.log('当前主题:', savedTheme);

    // 点击主题选项就切换
    document.addEventListener('click', function(e) {
        console.log('点击了:', e.target);
        const themeOption = e.target.closest('.theme-option');
        if (themeOption) {
            console.log('找到主题选项:', themeOption);
            e.preventDefault();
            const theme = themeOption.getAttribute('data-theme');
            console.log('切换到主题:', theme);

            // 直接应用主题
            document.documentElement.setAttribute('data-theme', theme);
            document.body.setAttribute('data-theme', theme);

            // 更新导航栏
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.className = navbar.className.replace(/bg-\w+/g, '');
                navbar.classList.add(`bg-${theme}`);
            }

            // 保存
            localStorage.setItem('user-theme', theme);
            console.log('主题切换完成:', theme);

            // 关闭菜单
            const dropdown = themeOption.closest('.dropdown-menu');
            if (dropdown) dropdown.classList.remove('show');
        }
    });
});
