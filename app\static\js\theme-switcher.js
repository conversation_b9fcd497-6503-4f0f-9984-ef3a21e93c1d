/**
 * 简单主题切换器 - 点击即应用
 */

// 简单的主题切换函数
function switchTheme(themeName) {
    // 直接应用主题
    document.documentElement.setAttribute('data-theme', themeName);
    document.body.setAttribute('data-theme', themeName);

    // 更新导航栏
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        // 移除所有主题类
        const themeClasses = ['bg-primary', 'bg-secondary', 'bg-success', 'bg-warning', 'bg-info', 'bg-danger', 'bg-dark',
                             'bg-classic-neutral', 'bg-modern-neutral', 'bg-noble-elegant', 'bg-royal-solemn',
                             'bg-deep-sea-tech', 'bg-soft-morandi', 'bg-minimal-dawn', 'bg-dark-neon', 'bg-nature-eco'];
        themeClasses.forEach(cls => navbar.classList.remove(cls));

        // 添加新主题类
        navbar.classList.add(`bg-${themeName}`);
    }

    // 保存到本地存储
    localStorage.setItem('user-theme', themeName);

    console.log(`主题已切换到: ${themeName}`);
}

// 加载保存的主题
function loadSavedTheme() {
    const savedTheme = localStorage.getItem('user-theme') || 'primary';
    switchTheme(savedTheme);
}

// 页面加载时应用保存的主题
document.addEventListener('DOMContentLoaded', function() {
    loadSavedTheme();

    // 绑定主题选项点击事件
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('theme-option') || e.target.closest('.theme-option')) {
            e.preventDefault();
            const themeOption = e.target.classList.contains('theme-option') ? e.target : e.target.closest('.theme-option');
            const theme = themeOption.getAttribute('data-theme');
            if (theme) {
                switchTheme(theme);

                // 关闭下拉菜单
                const dropdown = themeOption.closest('.dropdown-menu');
                if (dropdown) {
                    dropdown.classList.remove('show');
                }
            }
        }
    });
});

// 全局函数，供外部调用
window.switchTheme = switchTheme;
