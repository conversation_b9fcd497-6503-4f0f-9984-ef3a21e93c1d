// 超简单主题切换器 - 点击就换
document.addEventListener('DOMContentLoaded', function() {
    // 加载保存的主题
    const savedTheme = localStorage.getItem('user-theme') || 'primary';
    document.documentElement.setAttribute('data-theme', savedTheme);
    document.body.setAttribute('data-theme', savedTheme);

    // 点击主题选项就切换
    document.addEventListener('click', function(e) {
        const themeOption = e.target.closest('.theme-option');
        if (themeOption) {
            e.preventDefault();
            const theme = themeOption.getAttribute('data-theme');

            // 直接应用主题
            document.documentElement.setAttribute('data-theme', theme);
            document.body.setAttribute('data-theme', theme);

            // 更新导航栏
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.className = navbar.className.replace(/bg-\w+/g, '');
                navbar.classList.add(`bg-${theme}`);
            }

            // 保存
            localStorage.setItem('user-theme', theme);

            // 关闭菜单
            const dropdown = themeOption.closest('.dropdown-menu');
            if (dropdown) dropdown.classList.remove('show');
        }
    });
});
