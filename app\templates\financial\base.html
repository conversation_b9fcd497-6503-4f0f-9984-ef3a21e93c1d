{% extends "base.html" %}

{% block title %}财务管理{% endblock %}

{% block extra_css %}
<!-- 引入用友财务专业主题样式 -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/yonyou-theme.css') }}?v=2.0.0">
<style>
    /* 财务模块专用样式 - 基于用友主题 */

    /* 财务模块全局样式 - 使用用友标准 */
    .uf-financial-content {
        font-family: var(--uf-font-family);
        background: var(--uf-light);
        color: #333;
        line-height: var(--uf-line-height);
        padding: 12px;
        font-size: var(--uf-font-size);
    }

    /* 用友风格面包屑导航 */
    .uf-breadcrumb {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 12px;
        font-size: var(--uf-font-size);
        padding: 4px 0;
    }

    .uf-breadcrumb-item {
        color: #666;
        font-size: var(--uf-font-size);
    }

    .uf-breadcrumb-item a {
        color: var(--uf-primary);
        text-decoration: none;
        transition: var(--uf-transition);
    }

    .uf-breadcrumb-item a:hover {
        color: var(--uf-primary-dark);
        text-decoration: underline;
    }

    .uf-breadcrumb-item.active {
        color: #333;
        font-weight: 500;
    }

    .uf-breadcrumb-separator {
        color: #999;
        margin: 0 2px;
    }

    /* 用友风格工具栏 */
    .uf-financial-toolbar {
        background: var(--uf-toolbar-bg);
        padding: 8px 12px;
        border-radius: var(--uf-border-radius);
        box-shadow: var(--uf-box-shadow);
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid var(--uf-border);
        font-size: var(--uf-font-size);
    }

    .uf-financial-toolbar-title {
        font-size: 13px;
        font-weight: 600;
        color: var(--uf-primary);
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .uf-financial-toolbar-actions {
        display: flex;
        gap: 8px;
    }

    /* 用友风格卡片组件 - 使用标准uf-card */
    .uf-card {
        background: white;
        border-radius: var(--uf-border-radius);
        box-shadow: var(--uf-box-shadow);
        transition: var(--uf-transition);
        margin-bottom: 8px;
        border: 1px solid var(--uf-border);
        overflow: hidden;
    }

    .uf-card:hover {
        box-shadow: var(--uf-box-shadow-hover);
        border-color: var(--uf-primary);
    }

    .uf-card-header {
        padding: 8px 12px;
        border-bottom: 1px solid var(--uf-border);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: var(--uf-header-bg);
        font-size: var(--uf-font-size);
        font-weight: 600;
        color: var(--uf-primary);
    }

    .uf-card-body {
        padding: var(--uf-card-padding);
    }

    /* 用友风格表格样式 - 使用标准uf-table */
    .uf-table {
        width: 100%;
        border-collapse: collapse;
        font-size: var(--uf-font-size);
        background: white;
        font-family: var(--uf-font-family);
        border: 1px solid var(--uf-grid-border);
        margin-bottom: 0;
    }

    .uf-table th {
        background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
        border: 1px solid var(--uf-grid-border);
        padding: 4px 6px;
        text-align: center;
        font-weight: 600;
        color: var(--uf-primary);
        font-size: var(--uf-font-size);
        white-space: nowrap;
        height: var(--uf-table-header-height);
        vertical-align: middle;
    }

    .uf-table td {
        border: 1px solid var(--uf-grid-border);
        padding: 3px 6px;
        vertical-align: middle;
        font-size: var(--uf-font-size);
        height: var(--uf-table-row-height);
        line-height: 1.2;
    }

    .uf-table tbody tr:hover {
        background: var(--uf-row-hover);
    }

    .uf-table tbody tr:nth-child(even) {
        background: #fafafa;
    }

    .uf-table tbody tr:nth-child(even):hover {
        background: var(--uf-row-hover);
    }

    .uf-table tbody tr.selected {
        background: var(--uf-selected);
    }

    /* 用友风格按钮样式 - 使用标准uf-btn */
    .uf-btn {
        font-family: var(--uf-font-family);
        font-size: var(--uf-font-size);
        font-weight: 400;
        padding: 3px 8px;
        border: 1px solid var(--uf-border);
        border-radius: var(--uf-border-radius);
        background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
        color: #333;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 3px;
        text-decoration: none;
        transition: var(--uf-transition);
        min-height: var(--uf-btn-height);
        box-shadow: var(--uf-box-shadow);
        vertical-align: middle;
    }

    .uf-btn:hover {
        background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
        border-color: var(--uf-primary);
        color: var(--uf-primary);
        text-decoration: none;
        box-shadow: var(--uf-box-shadow-hover);
    }

    .uf-btn-primary {
        background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
        border-color: var(--uf-primary-dark);
        color: white;
        font-weight: 500;
    }

    .uf-btn-primary:hover {
        background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
        border-color: var(--uf-primary);
        color: white;
    }

    /* 表单控件 */
    .financial-form-control {
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.6;
        color: var(--financial-text);
        background: var(--financial-surface);
        border: 1px solid var(--financial-border);
        border-radius: var(--financial-radius);
        transition: var(--financial-transition);
    }

    .financial-form-control:hover {
        border-color: var(--financial-border);
    }

    .financial-form-control:focus {
        border-color: var(--financial-border);
        box-shadow: 0 0 0 3px rgba(226, 232, 240, 0.3);
        outline: none;
    }

    /* 状态指示器 */
    .financial-status {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 13px;
        font-weight: 500;
        transition: var(--financial-transition);
    }

    .financial-status.draft {
        background: #f1f5f9;
        color: var(--financial-text-secondary);
    }

    .financial-status.pending {
        background: #fef3c7;
        color: var(--financial-warning);
    }

    .financial-status.approved {
        background: #dcfce7;
        color: var(--financial-success);
    }

    .financial-status.posted {
        background: #dbeafe;
        color: var(--financial-primary);
    }

    /* 金额显示 */
    .financial-amount {
        font-family: 'Consolas', monospace;
        font-weight: 500;
        text-align: right;
    }

    .financial-amount.positive {
        color: var(--financial-success);
    }

    .financial-amount.negative {
        color: var(--financial-danger);
    }

    .financial-amount.zero {
        color: var(--financial-text-light);
    }

    /* 数据卡片 */
    .financial-data-card {
        background: var(--financial-surface);
        border-radius: var(--financial-radius);
        padding: 20px;
        box-shadow: var(--financial-shadow);
        transition: var(--financial-transition);
        border: 1px solid var(--financial-border);
    }

    .financial-data-card:hover {
        box-shadow: var(--financial-shadow-hover);
        transform: translateY(-2px);
    }

    .financial-data-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .financial-data-card-title {
        font-size: 14px;
        color: var(--financial-text-secondary);
        font-weight: 500;
    }

    .financial-data-card-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--financial-text);
        font-family: 'Consolas', monospace;
    }

    /* 响应式布局 */
    @media (max-width: 768px) {
        .financial-content {
            padding: 16px;
        }

        .financial-toolbar {
            flex-direction: column;
            gap: 16px;
            padding: 16px;
        }

        .financial-toolbar-actions {
            width: 100%;
            justify-content: space-between;
        }

        .financial-card-header,
        .financial-card-body {
            padding: 16px;
        }

        .financial-table {
            display: block;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="uf-financial-content">
    <!-- 用友风格面包屑导航 -->
    <nav class="uf-breadcrumb">
        <span class="uf-breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></span>
        <span class="uf-breadcrumb-separator">/</span>
        <span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></span>
        {% block breadcrumb %}{% endblock %}
    </nav>

    <!-- 用友风格工具栏 -->
    <div class="uf-financial-toolbar">
        <div class="uf-financial-toolbar-title">
            <i class="fas fa-calculator"></i>
            {% block page_title %}财务管理{% endblock %}
        </div>
        <div class="uf-financial-toolbar-actions">
            {% block page_actions %}{% endblock %}
        </div>
    </div>

    <!-- 页面内容 -->
    {% block financial_content %}{% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 财务系统通用函数

// 金额格式化
function formatAmount(amount) {
    if (amount === null || amount === undefined || amount === '') return '0.00';
    const num = parseFloat(amount);
    if (isNaN(num)) return '0.00';
    return num.toFixed(2);
}

// 金额显示（带货币符号和千分位）
function formatAmountDisplay(amount) {
    const formatted = formatAmount(amount);
    const withCommas = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return '<span class="financial-currency">¥</span>' + withCommas;
}

// 简单金额显示（仅货币符号）
function formatAmountSimple(amount) {
    const formatted = formatAmount(amount);
    return '¥' + formatted;
}

// 状态样式处理
function getStatusClass(status) {
    const statusMap = {
        'draft': 'draft',
        'pending': 'pending',
        'approved': 'approved',
        'posted': 'posted'
    };
    return statusMap[status.toLowerCase()] || 'draft';
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化状态标签
    document.querySelectorAll('.financial-status').forEach(function(element) {
        const status = element.textContent.trim();
        element.className = 'financial-status ' + getStatusClass(status);
    });

    // 初始化表格行选中效果
    document.querySelectorAll('.financial-table tbody tr').forEach(function(row) {
        row.addEventListener('click', function() {
            this.classList.toggle('selected');
        });
    });

    // 初始化数据卡片悬停效果
    document.querySelectorAll('.financial-data-card').forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% block financial_js %}{% endblock %}
{% endblock %}
