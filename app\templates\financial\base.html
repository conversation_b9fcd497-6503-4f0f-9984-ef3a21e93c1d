{% extends "base.html" %}

{% block title %}财务管理{% endblock %}

{% block extra_css %}
<style>
    /* 财务模块专用样式 - 基于 yonyou-theme.css */
    @import url('https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap');

    :root {
        /* 主色调 - 现代蓝色系 */
        --financial-primary: #2563eb;
        --financial-primary-hover: #1d4ed8;
        --financial-primary-active: #1e40af;
        --financial-success: #16a34a;
        --financial-warning: #d97706;
        --financial-danger: #dc2626;
        --financial-info: #0284c7;
        
        /* 中性色 */
        --financial-bg: #f8fafc;
        --financial-surface: #ffffff;
        --financial-border: #e2e8f0;
        --financial-text: #1e293b;
        --financial-text-secondary: #475569;
        --financial-text-light: #64748b;
        
        /* 尺寸 */
        --financial-radius: 6px;
        --financial-radius-lg: 8px;
        --financial-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
        --financial-shadow-lg: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        --financial-shadow-hover: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
        
        /* 动画 */
        --financial-transition: all 0.2s ease-in-out;
    }

    /* 财务模块全局样式 */
    .financial-content {
        font-family: 'Microsoft YaHei', sans-serif;
        background: var(--financial-bg);
        color: var(--financial-text);
        line-height: 1.6;
        padding: 24px;
    }

    /* 面包屑导航 */
    .financial-breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        font-size: 14px;
    }

    .financial-breadcrumb-item {
        color: var(--financial-text-secondary);
    }

    .financial-breadcrumb-item a {
        color: var(--financial-primary);
        text-decoration: none;
        transition: var(--financial-transition);
    }

    .financial-breadcrumb-item a:hover {
        color: var(--financial-primary-hover);
    }

    .financial-breadcrumb-item.active {
        color: var(--financial-text);
    }

    /* 工具栏 */
    .financial-toolbar {
        background: var(--financial-surface);
        padding: 16px 24px;
        border-radius: var(--financial-radius);
        box-shadow: var(--financial-shadow);
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid var(--financial-border);
    }

    .financial-toolbar-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--financial-text);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .financial-toolbar-actions {
        display: flex;
        gap: 12px;
    }

    /* 卡片组件 */
    .financial-card {
        background: var(--financial-surface);
        border-radius: var(--financial-radius);
        box-shadow: var(--financial-shadow);
        transition: var(--financial-transition);
        margin-bottom: 24px;
        border: 1px solid var(--financial-border);
    }

    .financial-card:hover {
        box-shadow: var(--financial-shadow-hover);
        transform: translateY(-1px);
    }

    .financial-card-header {
        padding: 16px 24px;
        border-bottom: 1px solid var(--financial-border);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .financial-card-body {
        padding: 24px;
    }

    /* 表格样式 */
    .financial-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 1rem;
        border: 1px solid var(--financial-border);
    }

    .financial-table th {
        background: var(--financial-bg);
        color: var(--financial-text);
        font-weight: 600;
        padding: 12px 16px;
        border-bottom: 1px solid var(--financial-border);
        text-align: left;
    }

    .financial-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--financial-border);
        border-right: 1px solid var(--financial-border);
        transition: var(--financial-transition);
    }

    .financial-table tbody tr {
        transition: var(--financial-transition);
    }

    .financial-table tbody tr:hover {
        background: var(--financial-bg);
        border-color: var(--financial-border) !important;
    }

    .financial-table tbody tr:hover td {
        border-color: var(--financial-border) !important;
    }

    .financial-table tbody tr.selected {
        background: #f8fafc;
        border-color: var(--financial-border) !important;
    }

    .financial-table tbody tr.selected td {
        border-color: var(--financial-border) !important;
    }

    /* 按钮样式 */
    .financial-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        font-size: 14px;
        font-weight: 500;
        border-radius: var(--financial-radius);
        transition: var(--financial-transition);
        cursor: pointer;
        border: none;
        gap: 8px;
    }

    .financial-btn-primary {
        background: var(--financial-primary);
        color: white;
    }

    .financial-btn-primary:hover {
        background: var(--financial-primary-hover);
        transform: translateY(-1px);
    }

    .financial-btn-primary:active {
        background: var(--financial-primary-active);
        transform: translateY(0);
    }

    /* 表单控件 */
    .financial-form-control {
        width: 100%;
        padding: 8px 12px;
        font-size: 14px;
        line-height: 1.6;
        color: var(--financial-text);
        background: var(--financial-surface);
        border: 1px solid var(--financial-border);
        border-radius: var(--financial-radius);
        transition: var(--financial-transition);
    }

    .financial-form-control:hover {
        border-color: var(--financial-border);
    }

    .financial-form-control:focus {
        border-color: var(--financial-border);
        box-shadow: 0 0 0 3px rgba(226, 232, 240, 0.3);
        outline: none;
    }

    /* 状态指示器 */
    .financial-status {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 13px;
        font-weight: 500;
        transition: var(--financial-transition);
    }

    .financial-status.draft {
        background: #f1f5f9;
        color: var(--financial-text-secondary);
    }

    .financial-status.pending {
        background: #fef3c7;
        color: var(--financial-warning);
    }

    .financial-status.approved {
        background: #dcfce7;
        color: var(--financial-success);
    }

    .financial-status.posted {
        background: #dbeafe;
        color: var(--financial-primary);
    }

    /* 金额显示 */
    .financial-amount {
        font-family: 'Consolas', monospace;
        font-weight: 500;
        text-align: right;
    }

    .financial-amount.positive {
        color: var(--financial-success);
    }

    .financial-amount.negative {
        color: var(--financial-danger);
    }

    .financial-amount.zero {
        color: var(--financial-text-light);
    }

    /* 数据卡片 */
    .financial-data-card {
        background: var(--financial-surface);
        border-radius: var(--financial-radius);
        padding: 20px;
        box-shadow: var(--financial-shadow);
        transition: var(--financial-transition);
        border: 1px solid var(--financial-border);
    }

    .financial-data-card:hover {
        box-shadow: var(--financial-shadow-hover);
        transform: translateY(-2px);
    }

    .financial-data-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .financial-data-card-title {
        font-size: 14px;
        color: var(--financial-text-secondary);
        font-weight: 500;
    }

    .financial-data-card-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--financial-text);
        font-family: 'Consolas', monospace;
    }

    /* 响应式布局 */
    @media (max-width: 768px) {
        .financial-content {
            padding: 16px;
        }

        .financial-toolbar {
            flex-direction: column;
            gap: 16px;
            padding: 16px;
        }

        .financial-toolbar-actions {
            width: 100%;
            justify-content: space-between;
        }

        .financial-card-header,
        .financial-card-body {
            padding: 16px;
        }

        .financial-table {
            display: block;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="financial-content">
    <!-- 面包屑导航 -->
    <nav class="financial-breadcrumb">
        <span class="financial-breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></span>
        <span class="financial-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></span>
        {% block breadcrumb %}{% endblock %}
    </nav>

    <!-- 工具栏 -->
    <div class="financial-toolbar">
        <div class="financial-toolbar-title">
            <i class="fas fa-calculator"></i>
            {% block page_title %}财务管理{% endblock %}
        </div>
        <div class="financial-toolbar-actions">
            {% block page_actions %}{% endblock %}
        </div>
    </div>

    <!-- 页面内容 -->
    {% block financial_content %}{% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 财务系统通用函数

// 金额格式化
function formatAmount(amount) {
    if (amount === null || amount === undefined || amount === '') return '0.00';
    const num = parseFloat(amount);
    if (isNaN(num)) return '0.00';
    return num.toFixed(2);
}

// 金额显示（带货币符号和千分位）
function formatAmountDisplay(amount) {
    const formatted = formatAmount(amount);
    const withCommas = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return '<span class="financial-currency">¥</span>' + withCommas;
}

// 简单金额显示（仅货币符号）
function formatAmountSimple(amount) {
    const formatted = formatAmount(amount);
    return '¥' + formatted;
}

// 状态样式处理
function getStatusClass(status) {
    const statusMap = {
        'draft': 'draft',
        'pending': 'pending',
        'approved': 'approved',
        'posted': 'posted'
    };
    return statusMap[status.toLowerCase()] || 'draft';
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化状态标签
    document.querySelectorAll('.financial-status').forEach(function(element) {
        const status = element.textContent.trim();
        element.className = 'financial-status ' + getStatusClass(status);
    });

    // 初始化表格行选中效果
    document.querySelectorAll('.financial-table tbody tr').forEach(function(row) {
        row.addEventListener('click', function() {
            this.classList.toggle('selected');
        });
    });

    // 初始化数据卡片悬停效果
    document.querySelectorAll('.financial-data-card').forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% block financial_js %}{% endblock %}
{% endblock %}
