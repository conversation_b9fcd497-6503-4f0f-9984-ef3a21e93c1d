<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style nonce="{{ csp_nonce }}">
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* A4横向打印设置 */
        @page {
            size: A4 landscape;
            margin: 1cm 0.8cm;
        }

        html, body {
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            background: white;
            width: 100%;
            height: 100%;
        }

        .container {
            width: 100%;
            max-width: none;
            padding: 0;
            margin: 0;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #000;
        }

        .subtitle {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
        }

        .print-info {
            font-size: 11px;
            color: #666;
            margin-bottom: 15px;
        }

        /* 菜单表格 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: fixed;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .menu-table th,
        .menu-table td {
            border: 1px solid #dee2e6;
            padding: 12px 8px;
            vertical-align: top;
            word-wrap: break-word;
        }

        .menu-table th {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            font-weight: 600;
            text-align: center;
            font-size: 14px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        /* 列宽设置 - 优化A4横向布局 */
        .date-col {
            width: 12%;
            min-width: 100px;
        }

        .meal-col {
            width: 29.33%;
            min-width: 200px;
        }

        /* 日期单元格 */
        .date-cell {
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-right: 2px solid #007bff;
        }

        .date-cell .weekday {
            font-size: 14px;
            margin-bottom: 4px;
            color: #007bff;
            font-weight: 600;
        }

        .date-cell .date {
            font-size: 11px;
            color: #6c757d;
        }

        /* 餐次单元格 */
        .meal-cell {
            text-align: left;
            padding: 8px;
            vertical-align: top;
            min-height: 80px;
            background-color: #fff;
            transition: background-color 0.2s ease;
        }

        .menu-table tbody tr:hover .meal-cell {
            background-color: #f8f9fa;
        }

        .meal-list {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            align-content: flex-start;
        }

        .meal-list li {
            display: inline-block;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            line-height: 1.3;
            white-space: nowrap;
            flex: 0 0 auto;
            max-width: calc(50% - 3px);
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
            color: #1976d2;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .meal-list li:hover {
            background: linear-gradient(135deg, #bbdefb, #90caf9);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
        }

        .meal-list li:before {
            content: none;
        }

        .no-meal {
            color: #9e9e9e;
            font-style: italic;
            font-size: 11px;
            padding: 15px;
            text-align: center;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px dashed #ddd;
        }

        /* 屏幕预览样式 */
        @media screen {
            body {
                background: #f5f5f5;
                padding: 20px;
            }

            .container {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin: 0 auto;
                max-width: 1200px;
                position: relative;
            }

            /* 优化后的按钮布局 */
            .action-buttons {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 8px;
                margin-bottom: 20px;
                padding: 12px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .action-buttons .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .btn-primary {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
            }

            .btn-info {
                background: linear-gradient(135deg, #17a2b8, #138496);
                color: white;
            }

            .btn-secondary {
                background: linear-gradient(135deg, #6c757d, #545b62);
                color: white;
            }

            .btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            .btn i {
                font-size: 12px;
            }

            /* 页眉优化 */
            .header {
                margin-bottom: 25px;
                padding: 15px 0;
                border-bottom: 2px solid #007bff;
            }

            .title {
                color: #007bff;
                margin-bottom: 10px;
            }

            .subtitle {
                font-weight: 500;
                color: #495057;
            }

            .print-info {
                color: #6c757d;
                font-size: 12px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                max-width: 100%;
                padding: 15px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 6px;
                align-items: stretch;
            }

            .action-buttons .btn {
                justify-content: center;
                padding: 10px 16px;
                font-size: 14px;
            }

            .menu-table th,
            .menu-table td {
                padding: 6px 4px;
                font-size: 11px;
            }

            .date-cell .weekday {
                font-size: 12px;
            }

            .date-cell .date {
                font-size: 10px;
            }

            .meal-list li {
                max-width: 100%;
                font-size: 11px;
                padding: 3px 6px;
            }

            .title {
                font-size: 18px;
            }

            .subtitle {
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .menu-table {
                font-size: 10px;
            }

            .meal-col {
                min-width: 120px;
            }

            .date-col {
                min-width: 60px;
            }

            .meal-list {
                gap: 3px;
            }

            .meal-list li {
                font-size: 10px;
                padding: 2px 4px;
            }
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .container {
                box-shadow: none;
                padding: 0;
                max-width: none;
            }

            .menu-table {
                box-shadow: none;
            }

            .menu-table th {
                background: #f5f5f5 !important;
                color: #000 !important;
                text-shadow: none;
            }

            .date-cell {
                background: #fafafa !important;
            }

            .meal-list li {
                background: #f8f9fa !important;
                border-color: #ccc !important;
                color: #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 操作按钮（仅在屏幕上显示） -->
        <div class="action-buttons no-print">
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-secondary">
                <i class="fas fa-list"></i> 返回列表
            </a>
            {% if weekly_menu.status == '计划中' %}
                <a href="{{ url_for('weekly_menu_v2.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start) }}" class="btn btn-info">
                    <i class="fas fa-edit"></i> 编辑菜单
                </a>
            {% endif %}
            <a href="{{ url_for('weekly_menu_v2.print_menu', id=weekly_menu.id) }}" class="btn btn-primary" target="_blank">
                <i class="fas fa-print"></i> 打印菜单
            </a>
        </div>

        <!-- 页眉 -->
        <div class="header">
            <div class="title">{{ weekly_menu.area.name }}周菜单计划表</div>
            <div class="subtitle">
                {{ weekly_menu.week_start.strftime('%Y年%m月%d日') }} 至 {{ weekly_menu.week_end.strftime('%Y年%m月%d日') }}
            </div>
            <div class="print-info">
                状态：<span class="status-badge status-{{ weekly_menu.status }}">{{ weekly_menu.status }}</span> |
                创建时间：{{ weekly_menu.created_at.strftime('%Y年%m月%d日') }} |
                创建者：{{ weekly_menu.creator.name if weekly_menu.creator else '未知' }}
            </div>
        </div>

        <!-- 菜单表格 -->
        <table class="menu-table">
            <thead>
                <tr>
                    <th class="date-col">日期</th>
                    <th class="meal-col">早餐</th>
                    <th class="meal-col">午餐</th>
                    <th class="meal-col">晚餐</th>
                </tr>
            </thead>
            <tbody>
                {% for date_str, day_data in week_data.items() %}
                <tr>
                    <td class="date-cell">
                        <div class="weekday">{{ day_data.weekday }}</div>
                        <div class="date">{{ date_str[5:] }}</div>
                    </td>
                    {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                    <td class="meal-cell">
                        {% if day_data.meals[meal_type] %}
                        <ul class="meal-list">
                            {% for recipe in day_data.meals[meal_type] %}
                            <li>{{ recipe.name }}</li>
                            {% endfor %}
                        </ul>
                        {% else %}
                        <div class="no-meal">未安排</div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
</body>
</html>
