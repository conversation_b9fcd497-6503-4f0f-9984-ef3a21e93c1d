{% extends "financial/base.html" %}

{% block page_title %}明细账管理{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">明细账管理</span>
{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-success" onclick="batchGenerateLedgers()">
    <i class="fas fa-magic uf-icon"></i> 批量生成
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格查询条件 -->
<div class="uf-card">
    <div class="uf-card-header">
        <div class="uf-card-header-title">
            <i class="fas fa-filter uf-card-header-icon"></i>
            查询条件
        </div>
    </div>
    <div class="uf-card-body">
        <form method="GET" class="uf-query-form">
            <div class="uf-form-row">
                <div class="uf-form-group">
                    <label class="uf-form-label">会计科目：</label>
                    <select class="uf-form-control" id="subject_id" name="subject_id" required>
                        <option value="">请选择科目</option>
                        {% for subject in subjects %}
                        <option value="{{ subject.id }}" {% if subject.id == subject_id %}selected{% endif %}>
                            {{ subject.code }} - {{ subject.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="uf-form-group">
                    <label class="uf-form-label">年份：</label>
                    <select class="uf-form-control" id="year" name="year" required>
                        {% for y in range(2020, 2030) %}
                        <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}年</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="uf-form-group">
                    <label class="uf-form-label">月份：</label>
                    <select class="uf-form-control" id="month" name="month" required>
                        {% for m in range(1, 13) %}
                        <option value="{{ m }}" {% if m == month %}selected{% endif %}>{{ m }}月</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="uf-form-group">
                    <div class="uf-btn-group">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-search uf-icon"></i> 查看明细账
                        </button>
                        <button type="button" class="uf-btn uf-btn-success" onclick="generateLedger()">
                            <i class="fas fa-magic uf-icon"></i> 生成明细账
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

{% if generation_status %}
<!-- 用友风格生成状态提示 -->
<div style="margin-bottom: 10px;">
    {% if generation_status.success %}
    <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 2px; padding: 12px; color: #155724;">
        <i class="fas fa-check-circle uf-icon"></i> {{ generation_status.message }}
        <br><small style="font-size: 11px;">记录数：{{ generation_status.records_count }}，期初余额：¥{{ "%.2f"|format(generation_status.opening_balance) }}，期末余额：¥{{ "%.2f"|format(generation_status.closing_balance) }}</small>
    </div>
    {% else %}
    <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 2px; padding: 12px; color: #721c24;">
        <i class="fas fa-exclamation-circle uf-icon"></i> {{ generation_status.message }}
    </div>
    {% endif %}
</div>
{% endif %}

{% if selected_subject and ledger_data %}
<!-- 用友风格科目信息 -->
<div class="uf-card" style="margin-bottom: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-list-alt uf-icon"></i> {{ ledger_data.subject.code }} - {{ ledger_data.subject.name }}
    </div>
    <div class="uf-card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; font-size: 12px;">
            <div>
                <strong>科目类型：</strong>{{ ledger_data.subject.subject_type }}
            </div>
            <div>
                <strong>余额方向：</strong>{{ ledger_data.subject.balance_direction }}
            </div>
            <div>
                <strong>期初余额：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.opening_balance) }}</span>
            </div>
            <div>
                <strong>期末余额：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.closing_balance) }}</span>
            </div>
            <div>
                <strong>本期借方：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.total_debit) }}</span>
            </div>
            <div>
                <strong>本期贷方：</strong><span class="uf-amount">¥{{ "%.2f"|format(ledger_data.total_credit) }}</span>
            </div>
            <div>
                <strong>发生笔数：</strong>{{ ledger_data.transaction_count }}
            </div>
            <div>
                <strong>账页期间：</strong>{{ year }}年{{ month }}月
            </div>
        </div>
    </div>
</div>

<!-- 用友财务软件专业明细账表格 -->
<div class="uf-card">
    <div class="uf-card-header">
        <i class="fas fa-table uf-icon"></i> 明细账记录
    </div>
    <div class="uf-card-body" style="padding: 0;">
        <table class="uf-table uf-ledger-table" style="margin: 0;">
            <thead>
                <tr>
                    <th style="width: 50px;">行号</th>
                    <th style="width: 80px;">日期</th>
                    <th style="width: 100px;">凭证号</th>
                    <th style="width: 160px;">摘要</th>
                    <th style="width: 90px;">借方金额</th>
                    <th style="width: 90px;">贷方金额</th>
                    <th style="width: 90px;">余额</th>
                    <th style="width: 60px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for record in ledger_data.records %}
                <tr {% if record.get('is_opening') %}style="background: linear-gradient(to bottom, #fff3cd 0%, #ffeaa7 100%); font-weight: 600;"{% elif record.get('is_closing') %}style="background: linear-gradient(to bottom, #d1ecf1 0%, #bee5eb 100%); font-weight: 600;"{% endif %}>
                    <td class="text-center" style="font-size: 11px;">{{ record.line_number }}</td>
                    <td class="text-center" style="font-size: 11px;">
                        {% if record.voucher_date %}
                            {{ record.voucher_date.strftime('%Y-%m-%d') if record.voucher_date.strftime else record.voucher_date }}
                        {% endif %}
                    </td>
                    <td class="text-center">
                        {% if record.voucher_number %}
                        <code class="uf-code">{{ record.voucher_number }}</code>
                        {% else %}
                        <span style="color: #999;">-</span>
                        {% endif %}
                    </td>
                    <td class="text-left" style="font-size: 11px; padding-left: 6px;">
                        {% if record.get('is_opening') %}
                            <strong>{{ record.summary }}</strong>
                        {% elif record.get('is_closing') %}
                            <strong>{{ record.summary }}</strong>
                        {% else %}
                            {{ record.summary }}
                        {% endif %}
                    </td>
                    <td class="uf-amount-col">
                        {% if record.debit_amount > 0 %}
                            {{ "%.2f"|format(record.debit_amount) }}
                        {% else %}
                            <span style="color: #ccc;">-</span>
                        {% endif %}
                    </td>
                    <td class="uf-amount-col">
                        {% if record.credit_amount > 0 %}
                            {{ "%.2f"|format(record.credit_amount) }}
                        {% else %}
                            <span style="color: #ccc;">-</span>
                        {% endif %}
                    </td>
                    <td class="uf-amount-col" style="font-weight: 600; background: #f8f9fa;">
                        {{ "%.2f"|format(record.balance) }}
                    </td>
                    <td class="text-center">
                        {% if record.get('voucher_id') %}
                        <a href="{{ url_for('financial.view_voucher', id=record.voucher_id) }}"
                           class="uf-btn uf-btn-sm uf-btn-info" title="查看凭证" style="padding: 1px 4px;">
                            <i class="fas fa-eye" style="font-size: 10px;"></i>
                        </a>
                        {% else %}
                        <span style="color: #999;">-</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

{% else %}
<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 2px; padding: 16px; text-align: center; color: #856404;">
    <i class="fas fa-info-circle uf-icon"></i> 请选择会计科目和年月，然后点击"查看明细账"或"生成明细账"
</div>
{% endif %}

{% if not selected_subject %}
<!-- 用友风格使用说明 -->
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-info-circle uf-icon"></i> 明细账功能说明
    </div>
    <div class="uf-card-body">
        <div style="font-size: 12px; line-height: 1.6;">
            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">什么是明细账？</h6>
            <p style="margin-bottom: 12px;">明细账是按照会计科目设置的，用来分类登记某一类经济业务，提供有关明细核算资料的账簿。</p>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">明细账的特点：</h6>
            <ul style="margin-bottom: 12px; padding-left: 20px;">
                <li style="margin-bottom: 4px;"><strong>按月生成</strong>：每个科目按月份生成独立的明细账页</li>
                <li style="margin-bottom: 4px;"><strong>连续记录</strong>：从期初余额开始，按时间顺序记录每笔业务</li>
                <li style="margin-bottom: 4px;"><strong>余额结转</strong>：每笔业务后自动计算并更新余额</li>
                <li style="margin-bottom: 4px;"><strong>标准格式</strong>：符合会计账簿的标准格式要求</li>
            </ul>

            <h6 style="color: var(--uf-primary); margin-bottom: 8px; font-size: 13px;">使用步骤：</h6>
            <ol style="margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 4px;">选择要查看的会计科目</li>
                <li style="margin-bottom: 4px;">选择年份和月份</li>
                <li style="margin-bottom: 4px;">点击"生成明细账"按钮（首次使用）</li>
                <li style="margin-bottom: 4px;">点击"查看明细账"查看已生成的明细账</li>
                <li style="margin-bottom: 0;">可以批量生成所有有发生额科目的明细账</li>
            </ol>
        </div>
    </div>
</div>
{% endif %}

<!-- 用友风格导出和打印功能 -->
{% if selected_subject and ledger_data %}
<div class="uf-card" style="margin-top: 10px;">
    <div class="uf-card-header">
        <i class="fas fa-tools uf-icon"></i> 操作功能
    </div>
    <div class="uf-card-body">
        <div class="uf-btn-group">
            <button type="button" class="uf-btn uf-btn-success" onclick="exportDetailLedger()">
                <i class="fas fa-file-excel uf-icon"></i> 导出Excel
            </button>
            <button type="button" class="uf-btn uf-btn-info" onclick="printDetailLedger()">
                <i class="fas fa-print uf-icon"></i> 打印明细账
            </button>
            <button type="button" class="uf-btn uf-btn-warning" onclick="regenerateLedger()">
                <i class="fas fa-redo uf-icon"></i> 重新生成
            </button>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block financial_js %}
<script>
// 生成单个科目明细账
function generateLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId) {
        alert('请先选择科目');
        return;
    }

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.generate_detail_ledger_api") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            subject_id: parseInt(subjectId),
            year: parseInt(year),
            month: parseInt(month)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 重新加载页面显示生成的明细账
            window.location.href = `?subject_id=${subjectId}&year=${year}&month=${month}`;
        } else {
            alert('生成失败：' + data.message);
        }
    })
    .catch(error => {
        alert('生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 批量生成明细账
function batchGenerateLedgers() {
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!year || !month) {
        alert('请选择年份和月份');
        return;
    }

    if (!confirm(`确定要批量生成 ${year}年${month}月 所有有发生额科目的明细账吗？`)) {
        return;
    }

    // 显示加载状态
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 批量生成中...';
    btn.disabled = true;

    fetch('{{ url_for("financial.batch_generate_detail_ledgers") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            year: parseInt(year),
            month: parseInt(month),
            subject_ids: []  // 空数组表示所有有发生额的科目
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 刷新页面
            window.location.reload();
        } else {
            alert('批量生成失败：' + data.message);
        }
    })
    .catch(error => {
        alert('批量生成失败：' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

// 重新生成明细账
function regenerateLedger() {
    if (!confirm('确定要重新生成明细账吗？这将覆盖现有的明细账数据。')) {
        return;
    }
    generateLedger();
}

// 导出明细账
function exportDetailLedger() {
    const subjectId = document.getElementById('subject_id').value;
    const year = document.getElementById('year').value;
    const month = document.getElementById('month').value;

    if (!subjectId || !year || !month) {
        alert('请先选择科目和年月');
        return;
    }

    const startDate = `${year}-${month.padStart(2, '0')}-01`;
    const url = `{{ url_for('financial.export_report', report_type='detail_ledger') }}?subject_id=${subjectId}&start_date=${startDate}&end_date=${startDate}`;
    window.open(url, '_blank');
}

// 打印明细账
function printDetailLedger() {
    window.print();
}

// 科目选择变化时的处理
document.getElementById('subject_id').addEventListener('change', function() {
    // 不自动提交，让用户手动选择操作
});

// 添加明细账表格样式
document.addEventListener('DOMContentLoaded', function() {
    // 为明细账表格添加特殊样式
    const ledgerTable = document.querySelector('.ledger-table');
    if (ledgerTable) {
        ledgerTable.style.fontSize = '0.9rem';
        ledgerTable.style.fontFamily = 'monospace';
    }
});
</script>

<style>
/* 用友财务软件专业明细账表格样式 */
.uf-ledger-table {
    font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
    font-size: 11px;
    border-collapse: collapse;
}

.uf-ledger-table th {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%) !important;
    color: white !important;
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    font-size: 11px;
    border: 1px solid var(--uf-grid-border);
    padding: 4px 6px;
    height: 24px;
}

.uf-ledger-table td {
    vertical-align: middle;
    padding: 3px 6px;
    font-size: 11px;
    border: 1px solid var(--uf-grid-border);
    height: 22px;
    line-height: 1.2;
}

.uf-ledger-table .uf-amount-col {
    font-family: 'Courier New', 'Times New Roman', monospace;
    font-size: 11px;
    text-align: right;
    padding-right: 8px;
    white-space: nowrap;
}

/* 明细账专用样式 */
.uf-ledger-table tbody tr:hover {
    background: var(--uf-row-hover) !important;
}

.uf-ledger-table tbody tr:nth-child(even) {
    background: #fafafa;
}

.uf-ledger-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover) !important;
}

/* 期初期末行特殊样式 */
.uf-ledger-table tr[style*="background: linear-gradient"] {
    font-weight: 600;
}

.uf-ledger-table tr[style*="background: linear-gradient"]:hover {
    opacity: 0.9;
}

/* 打印样式 */
@media print {
    .uf-toolbar,
    .uf-search-form,
    .uf-btn-group,
    .uf-card-header {
        display: none !important;
    }

    .uf-ledger-table {
        font-size: 9px;
        page-break-inside: avoid;
    }

    .uf-ledger-table th {
        font-size: 9px;
        padding: 2px 4px;
    }

    .uf-ledger-table td {
        font-size: 9px;
        padding: 2px 4px;
    }

    .uf-card {
        border: none !important;
        box-shadow: none !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }

    .uf-card-body {
        padding: 0 !important;
    }

    body {
        font-size: 9px;
    }

    /* 打印时隐藏操作列 */
    .uf-ledger-table th:last-child,
    .uf-ledger-table td:last-child {
        display: none;
    }
}
</style>
{% endblock %}
