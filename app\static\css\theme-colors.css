/* 专业主题颜色系统 - 基于色彩心理学和设计原理 */
:root {
  /* 默认主题颜色 (Ocean Blue - 海洋蓝) */
  --theme-primary: #2563eb;
  --theme-primary-light: #60a5fa; /* 补全缺失的浅色变量 */
  --theme-primary-dark: #1d4ed8; /* 补全缺失的深色变量 */
  --theme-primary-50: #eff6ff;
  --theme-primary-100: #dbeafe;
  --theme-primary-200: #bfdbfe;
  --theme-primary-300: #93c5fd;
  --theme-primary-400: #60a5fa;
  --theme-primary-500: #3b82f6;
  --theme-primary-600: #2563eb;
  --theme-primary-700: #1d4ed8;
  --theme-primary-800: #1e40af;
  --theme-primary-900: #1e3a8a;
  --theme-primary-rgb: 37, 99, 235;

  /* 语义化颜色 */
  --theme-success: #059669;
  --theme-success-light: #10b981;
  --theme-success-dark: #047857;
  --theme-info: #0891b2;
  --theme-info-light: #06b6d4;
  --theme-info-dark: #0e7490;
  --theme-warning: #d97706;
  --theme-warning-light: #f59e0b;
  --theme-warning-dark: #b45309;
  --theme-danger: #dc2626;
  --theme-danger-light: #ef4444;
  --theme-danger-dark: #b91c1c;

  /* 中性色调 */
  --theme-gray-50: #f9fafb;
  --theme-gray-100: #f3f4f6;
  --theme-gray-200: #e5e7eb;
  --theme-gray-300: #d1d5db;
  --theme-gray-400: #9ca3af;
  --theme-gray-500: #6b7280;
  --theme-gray-600: #4b5563;
  --theme-gray-700: #374151;
  --theme-gray-800: #1f2937;
  --theme-gray-900: #111827;
}

/* 专业主题配色方案 */

/* 1. 海洋蓝主题 (Ocean Blue) - 专业、信任、稳定 */
[data-theme="primary"] {
  /* 核心海洋蓝色系 - 基于您的分析优化 */
  --theme-primary: #001F3F; /* 海军蓝 */
  --theme-primary-light: #7FDBFF; /* 浅水蓝 */
  --theme-primary-dark: #001122; /* 深海蓝 */
  --theme-primary-rgb: 0, 31, 63;
  --theme-accent: #FF6B6B; /* 珊瑚橙 - 互补对比色 */
  --theme-surface: #F8F9FA; /* 珍珠白 */
  --theme-surface-dark: #CCD6DD; /* 银灰 */
  --theme-coral: #FF6B6B;
  --theme-pearl: #F8F9FA;
  --theme-silver: #CCD6DD;

  /* 波浪纹理背景变量 */
  --wave-pattern: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23001F3F' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* 2. 现代主题 (Modern Theme) - 极简、未来感、几何美学 */
[data-theme="secondary"] {
  /* 现代科技配色 - 优化对比度和可读性 */
  --theme-primary: #333333; /* 碳素黑 */
  --theme-primary-light: #666666; /* 中灰色 - 更好的对比度 */
  --theme-primary-dark: #1a1a1a; /* 深碳黑 */
  --theme-primary-rgb: 51, 51, 51;
  --theme-accent: #0099cc; /* 科技蓝 - 降低亮度，提高可读性 */
  --theme-surface: #f8f9fc; /* 微质感背景 */
  --theme-surface-dark: #e2e8f0;
  --theme-neon-blue: #0099cc; /* 调整霓虹蓝亮度 */
  --theme-neon-pink: #cc0066; /* 调整电子粉亮度 */
  --theme-neon-green: #00cc66; /* 调整荧光绿亮度 */
  --theme-space-silver: #E5E5E5;

  /* 移除模糊效果，保持文字清晰 */
  --glass-effect: none;
  --glow-effect: 0 0 5px currentColor; /* 减少发光强度 */
}

/* 3. 自然绿主题 (Nature Green) - 健康、成长、和谐 */
[data-theme="success"] {
  /* 进阶自然配色 - 基于您的深度优化方案 */
  --theme-primary: #4A7856; /* 苔藓绿 */
  --theme-primary-light: #87A96B; /* 竹青 */
  --theme-primary-dark: #2d4a35; /* 深森林绿 */
  --theme-primary-rgb: 74, 120, 86;
  --theme-accent: #BC8A5F; /* 黏土棕 */
  --theme-surface: #f0fdf4; /* 清新背景 */
  --theme-surface-dark: #dcfce7;
  --theme-moss: #4A7856;
  --theme-bamboo: #87A96B;
  --theme-clay: #BC8A5F;

  /* 叶片投影动画变量 */
  --leaf-shadow: drop-shadow(2px 2px 4px rgba(74, 120, 86, 0.3));
  --ripple-color: rgba(74, 120, 86, 0.2);
}

/* 4. 活力橙主题 (Energy Orange) - 活力、创新、温暖 */
[data-theme="warning"] {
  /* 能量橙配色 - 基于您的分析优化 */
  --theme-primary: #FF6B35; /* 日出色 */
  --theme-primary-light: #FF9F1C; /* 琥珀橙 */
  --theme-primary-dark: #e55a2b; /* 深日出色 */
  --theme-primary-rgb: 255, 107, 53;
  --theme-accent: #0353A4; /* 深海蓝 - 互补色 */
  --theme-surface: #FDFCDC; /* 云朵白 */
  --theme-surface-dark: #fef3c7;
  --theme-sunrise: #FF6B35;
  --theme-amber: #FF9F1C;
  --theme-cloud: #FDFCDC;
  --theme-deep-blue: #0353A4;
  --theme-alert-red: #D90429; /* 警戒红 */

  /* 火焰动效变量 */
  --flame-gradient: linear-gradient(45deg, #FF6B35, #FF9F1C, #FFD700);
  --energy-pulse: 0 0 20px rgba(255, 107, 53, 0.6);
}

/* 5. 优雅紫主题 (Elegant Purple) - 创新、优雅、神秘 */
[data-theme="info"] {
  /* 神秘紫配色 - 基于您的分析优化 */
  --theme-primary: #B399D4; /* 薰衣草紫 (饱和度<60%) */
  --theme-primary-light: #c9b3e0; /* 浅薰衣草 */
  --theme-primary-dark: #7851A9; /* 皇室紫 */
  --theme-primary-rgb: 179, 153, 212;
  --theme-accent: #228B22; /* 祖母绿 - 对比色 */
  --theme-surface: #faf5ff; /* 紫色调背景 */
  --theme-surface-dark: #f3e8ff;
  --theme-lavender: #B399D4;
  --theme-royal: #7851A9;
  --theme-champagne: #E5C100; /* 香槟金 */
  --theme-marble: #D3D3D3; /* 大理石灰 */
  --theme-emerald: #228B22;

  /* VIP烫金效果 */
  --gold-glow: 0 0 10px rgba(229, 193, 0, 0.5);
  --vip-gradient: linear-gradient(45deg, #E5C100, #FFD700, #E5C100);
}

/* 6. 深邃红主题 (Deep Red) - 力量、重要、警示 */
[data-theme="danger"] {
  /* 深邃红配色 - 基于您的分析优化 */
  --theme-primary: #C91F37; /* 中国红 */
  --theme-primary-light: #dc3545; /* 亮红 */
  --theme-primary-dark: #900020; /* 勃艮第红 */
  --theme-primary-rgb: 201, 31, 55;
  --theme-accent: #B87333; /* 金属铜色 (非金色) */
  --theme-surface: #FFFFF0; /* 象牙白 - 确保可读性 */
  --theme-surface-dark: #fecaca;
  --theme-china-red: #C91F37;
  --theme-burgundy: #900020;
  --theme-copper: #B87333;
  --theme-ivory: #FFFFF0;

  /* 深红主题必须使用象牙白文字 */
  --theme-text-on-primary: #FFFFF0;
  --theme-metallic-sheen: linear-gradient(45deg, #B87333, #CD7F32, #B87333);
}

/* 7. 用友财务主题 (UFIDA Theme) - 专业、稳重、高效 */
[data-theme="yonyou"] {
  /* 用友NC Cloud/U8 财务风格配色系统 */
  --theme-primary: #1E88E5; /* 用友主蓝色 */
  --theme-primary-light: #64B5F6;
  --theme-primary-dark: #1565C0;
  --theme-primary-rgb: 30, 136, 229;
  --theme-accent: #43A047; /* 用友绿色 */
  --theme-surface: #F5F7FA; /* 用友浅灰背景 */
  --theme-surface-dark: #E3F2FD; /* 用友浅蓝背景 */
  --theme-text: #212121;
  --theme-text-secondary: #757575;

  /* 用友主题层级系统 */
  --theme-card-bg: #FFFFFF; /* 卡片背景 */
  --theme-border: #E0E0E0; /* 边框颜色 */
  --theme-hover: #E3F2FD; /* 悬停背景 */
  --theme-selected: #BBDEFB; /* 选中背景 */

  /* 用友主题特殊变量 */
  --bs-body-bg: #F5F7FA;
  --bs-body-color: #212121;
  --bs-border-color: #E0E0E0;

  /* 用友风格阴影效果 */
  --yonyou-shadow: 0 2px 8px rgba(30, 136, 229, 0.1);
  --yonyou-card-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* === 经典优雅配色方案 === */

/* 8. 经典中性风 - 米白色·深棕色·金色 */
[data-theme="classic-neutral"] {
  --theme-primary: #8b4513;
  --theme-primary-light: #a0522d;
  --theme-primary-dark: #654321;
  --theme-primary-rgb: 139, 69, 19;
  --theme-accent: #daa520;
  --theme-surface: #faf5f0;
  --theme-surface-dark: #f5f5dc;
  --theme-gold: #daa520;
}

/* 9. 现代中性风 - 灰白色·深灰色·香槟金 */
[data-theme="modern-neutral"] {
  --theme-primary: #4a5568;
  --theme-primary-light: #718096;
  --theme-primary-dark: #2d3748;
  --theme-primary-rgb: 74, 85, 104;
  --theme-accent: #d4af37;
  --theme-surface: #f7fafc;
  --theme-surface-dark: #edf2f7;
  --theme-champagne: #d4af37;
}

/* 10. 复古典雅风 - 维多利亚时代特征 */
[data-theme="vintage-elegant"] {
  /* 维多利亚时代配色 - 基于您的分析优化 */
  --theme-primary: #004225; /* 维多利亚绿 */
  --theme-primary-light: #2d5a3d; /* 浅维多利亚绿 */
  --theme-primary-dark: #002818; /* 深维多利亚绿 */
  --theme-primary-rgb: 0, 66, 37;
  --theme-accent: #8B4513; /* 古典棕 */
  --theme-surface: #F3E5AB; /* 羊皮纸色 */
  --theme-surface-dark: #e6d89a;
  --theme-victoria-green: #004225;
  --theme-parchment: #F3E5AB;
  --theme-antique-gold: #B8860B; /* 做旧金色 */

  /* 浮雕纹理效果 */
  --emboss-effect: inset 1px 1px 2px rgba(0, 0, 0, 0.1), inset -1px -1px 2px rgba(255, 255, 255, 0.1);
  --paper-texture: url("data:image/svg+xml,%3Csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.05'/%3E%3C/svg%3E");

  /* 衬线字体支持 */
  --serif-font: 'Times New Roman', 'Georgia', serif;
}

/* 11. 贵族典雅风 - 藏青色·深紫色·银色 */
[data-theme="noble-elegant"] {
  --theme-primary: #191970;
  --theme-primary-light: #483d8b;
  --theme-primary-dark: #0f0f4d;
  --theme-primary-rgb: 25, 25, 112;
  --theme-accent: #663399;
  --theme-surface: #f8f8ff;
  --theme-surface-dark: #f0f0ff;
  --theme-silver: #c0c0c0;
}

/* 12. 清新优雅风 - 优化配色，提高对比度 */
[data-theme="fresh-elegant"] {
  --theme-primary: #4682b4; /* 钢蓝色 - 更深，对比度更好 */
  --theme-primary-light: #87ceeb; /* 天蓝色作为浅色 */
  --theme-primary-dark: #2f4f4f; /* 深石板灰 */
  --theme-primary-rgb: 70, 130, 180;
  --theme-accent: #ff69b4; /* 热粉色 - 更鲜明的对比 */
  --theme-surface: #f8f8ff; /* 幽灵白 */
  --theme-surface-dark: #f0f8ff; /* 爱丽丝蓝 */
}

/* 13. 春日清新风 - 优化配色，提高可读性 */
[data-theme="spring-fresh"] {
  --theme-primary: #228b22; /* 森林绿 - 更深，对比度更好 */
  --theme-primary-light: #32cd32; /* 酸橙绿 */
  --theme-primary-dark: #006400; /* 深绿色 */
  --theme-primary-rgb: 34, 139, 34;
  --theme-accent: #ffd700; /* 金色 - 更鲜明的对比 */
  --theme-surface: #f0fff0; /* 蜜瓜色 */
  --theme-surface-dark: #f5fffa; /* 薄荷奶油色 */
}

/* 14. 华丽庄重风 - 金色·黑色·红色 */
[data-theme="luxury-solemn"] {
  --theme-primary: #ffd700;
  --theme-primary-light: #ffed4e;
  --theme-primary-dark: #b8860b;
  --theme-primary-rgb: 255, 215, 0;
  --theme-accent: #dc143c;
  --theme-surface: #fffaf0;
  --theme-surface-dark: #2f2f2f;
  --theme-black: #000000;
}

/* 15. 皇室庄重风 - 深紫色·深红色·黑色 */
[data-theme="royal-solemn"] {
  --theme-primary: #4b0082;
  --theme-primary-light: #663399;
  --theme-primary-dark: #301934;
  --theme-primary-rgb: 75, 0, 130;
  --theme-accent: #8b0000;
  --theme-surface: #f8f8ff;
  --theme-surface-dark: #2f2f2f;
  --theme-black: #000000;
}

/* 专业界面元素样式应用 */

/* 导航栏样式 - 增强优先级 */
.navbar,
.navbar.bg-primary,
.navbar.bg-secondary,
.navbar.bg-success,
.navbar.bg-warning,
.navbar.bg-info,
.navbar.bg-danger,
.navbar.bg-dark,
.navbar.bg-classic-neutral,
.navbar.bg-modern-neutral,
.navbar.bg-vintage-elegant,
.navbar.bg-noble-elegant,
.navbar.bg-fresh-elegant,
.navbar.bg-spring-fresh,
.navbar.bg-luxury-solemn,
.navbar.bg-royal-solemn {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-dark, #1d4ed8) 100%) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: none;
  z-index: 1030; /* 确保导航栏在最上层 */
}

.navbar-brand {
  color: white !important;
  font-weight: 600 !important;
  font-size: 1.25rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important; /* 增强品牌名称的阴影 */
}

.navbar-nav .nav-link {
  color: white !important;
  font-weight: 500 !important;
  font-size: 1rem !important; /* 明确设置字体大小 */
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 0 2px;
  padding: 8px 12px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important; /* 增加文字阴影增强对比度 */
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important; /* 轻微上移效果 */
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--theme-primary);
  border: none;
  border-radius: 8px;
  font-weight: normal !important;
  padding: 10px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.2);
}

.btn-primary:hover {
  background-color: var(--theme-primary-dark);
  /* 移除transform效果，避免文字模糊 */
  box-shadow: 0 3px 6px rgba(var(--theme-primary-rgb), 0.3);
}

.btn-primary:focus,
.btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.5);
}

.btn-primary:active {
  /* 移除transform效果，避免文字模糊 */
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.3);
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* === 统一卡片标题样式 - 优化字体层级和配色 === */
.card-header {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-dark, #1d4ed8) 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 600 !important; /* 增强字体权重 */
  font-size: 1rem !important; /* 提升字体大小为16px */
  padding: 0.875rem 1.25rem !important; /* 增加内边距，更舒适 */
  line-height: 1.3 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important; /* 增强阴影效果 */
  letter-spacing: 0.025em !important; /* 增加字母间距 */
}

/* 卡片标题内的所有文字元素统一样式 */
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6,
.card-header .h1,
.card-header .h2,
.card-header .h3,
.card-header .h4,
.card-header .h5,
.card-header .h6 {
  color: white !important;
  font-size: 1rem !important; /* 统一所有标题大小为16px */
  font-weight: 600 !important; /* 增强字体权重 */
  margin: 0 !important;
  line-height: 1.3 !important;
  letter-spacing: 0.025em !important;
}

/* 特殊背景色类的处理 */
.card-header.bg-primary {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-light, #60a5fa) 100%) !important;
  color: white !important;
}

/* 表单控件样式 */
.form-control {
  border: 1px solid var(--theme-gray-200, #ced4da);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.15);
  background-color: var(--theme-surface, #ffffff);
}

/* 徽章样式 */
.badge-primary {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

/* 补充其他badge样式，确保颜色对比度 */
.badge-secondary {
  background-color: #6c757d;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-success {
  background-color: #28a745;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529; /* 深色文字确保对比度 */
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-info {
  background-color: #17a2b8;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-light {
  background-color: #f8f9fa;
  color: #212529; /* 深色文字 */
  border-radius: 6px;
  font-weight: 500;
  padding: 6px 10px;
}

.badge-dark {
  background-color: #343a40;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  padding: 6px 10px;
}

/* 文本颜色 */
.text-primary {
  color: var(--theme-primary) !important;
}

/* 边框颜色 */
.border-primary {
  border-color: var(--theme-primary) !important;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--theme-primary-dark);
  text-decoration: none;
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 8px;
  background-color: var(--theme-surface, #ffffff);
}

.dropdown-item {
  border-radius: 8px;
  padding: 10px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 1rem; /* 与导航栏链接字体大小保持一致 */
}

.dropdown-item:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  color: var(--theme-primary);
  transform: translateX(4px);
}

.dropdown-item.active,
.dropdown-item:active {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
}

/* 表格样式 */
.table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table thead th {
  background: transparent;
  color: white;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  padding: 12px 16px;
  transition: none;
}

/* 最后一列表头不需要右边框 */
.table thead th:last-child {
  border-right: none;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.05);
  /* 移除缩放效果，保持简洁 */
}

.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

/* 分页样式 */
.pagination {
  gap: 4px;
}

.pagination .page-link {
  color: var(--theme-primary);
  border: 2px solid var(--theme-gray-200, #e5e7eb);
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  border-color: var(--theme-primary);
  color: white;
  box-shadow: 0 4px 8px rgba(var(--theme-primary-rgb), 0.3);
}

/* 警告框样式 */
.alert {
  border: none;
  border-radius: 12px;
  padding: 16px 20px;
  font-weight: 500;
}

.alert-primary {
  background: linear-gradient(135deg, rgba(var(--theme-primary-rgb), 0.1) 0%, rgba(var(--theme-primary-rgb), 0.05) 100%);
  color: var(--theme-primary-dark);
  border-left: 4px solid var(--theme-primary);
}

/* 进度条样式 */
.progress {
  height: 8px;
  border-radius: 8px;
  background-color: var(--theme-gray-200, #e5e7eb);
}

.progress-bar {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 图标样式 */
.text-primary i {
  color: var(--theme-primary) !important;
}

/* === 基于您分析的高级功能 === */

/* 1. 动态响应机制 - 环境光自动调节 */
@media (prefers-color-scheme: dark) {
  :root {
    --auto-saturation: 0.8; /* 夜间降低饱和度 */
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --auto-saturation: 1.0; /* 日间正常饱和度 */
  }
}

/* 2. 无障碍设计 - 色弱用户支持 */
@media (prefers-contrast: high) {
  :root {
    --theme-primary: color-mix(in srgb, var(--theme-primary) 80%, black 20%);
    --contrast-boost: 1.5;
  }
}

/* 3. 应急对比度切换 */
.emergency-contrast {
  --theme-primary: #000000 !important;
  --theme-surface: #ffffff !important;
  --theme-text: #000000 !important;
  filter: contrast(2) !important;
}

/* 4. 自然绿主题色弱增强模式 */
[data-theme="success"].colorblind-enhanced {
  --theme-primary: #7CB342; /* 黄绿色增强 */
  --theme-accent: #FF9800; /* 橙色对比 */
}

/* 5. 移动端拇指热区配色权重 */
@media (max-width: 768px) {
  .btn-primary,
  .nav-link,
  .dropdown-item {
    min-height: 44px; /* 符合拇指触控标准 */
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
    box-shadow: 0 2px 8px rgba(var(--theme-primary-rgb), 0.3);
  }
}

/* 6. 文化适配 - 避免不当配色组合 */
.cultural-safe {
  /* 避免大面积黑白配 (东亚市场) */
  background: var(--theme-surface);
  color: var(--theme-primary);
}

/* 7. 用友主题专业提示 */
[data-theme="yonyou"] .professional-tip {
  background: rgba(30, 136, 229, 0.1);
  color: var(--theme-primary);
  border: 1px solid rgba(30, 136, 229, 0.2);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
}

/* 全局动画 - 优化版 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* 用友主题特殊处理 */
[data-theme="yonyou"] {
  --bs-body-bg: #F5F7FA;
  --bs-body-color: #212121;
}

[data-theme="yonyou"] .card {
  background-color: #FFFFFF;
  color: #212121;
  border: 1px solid #E0E0E0;
  box-shadow: var(--yonyou-card-shadow);
}

/* === 主题特定动画效果 === */

/* 移除表格背景动画 - 保持简洁专业 */

/* 现代主题 - 霓虹发光效果 */
[data-theme="secondary"] .btn-primary {
  box-shadow: var(--glow-effect);
  border: 1px solid var(--theme-neon-blue);
}

[data-theme="secondary"] .btn-primary:hover {
  animation: neonPulse 1.5s ease-in-out infinite alternate;
}

@keyframes neonPulse {
  from { box-shadow: 0 0 5px var(--theme-neon-blue); }
  to { box-shadow: 0 0 20px var(--theme-neon-blue), 0 0 30px var(--theme-neon-blue); }
}

/* 自然绿主题 - 保持简洁，移除过度动画 */
[data-theme="success"] .btn:active {
  animation: rippleEffect 0.6s ease-out;
}

@keyframes rippleEffect {
  0% { box-shadow: 0 0 0 0 var(--ripple-color); }
  70% { box-shadow: 0 0 0 10px rgba(74, 120, 86, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 120, 86, 0); }
}

/* 活力橙主题 - 火焰燃烧效果 */
[data-theme="warning"] .progress-bar {
  background: var(--flame-gradient);
  animation: flameFlicker 2s ease-in-out infinite;
}

@keyframes flameFlicker {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  25% { filter: hue-rotate(10deg) brightness(1.1); }
  50% { filter: hue-rotate(-5deg) brightness(0.9); }
  75% { filter: hue-rotate(5deg) brightness(1.05); }
}

[data-theme="warning"] .btn-primary:hover {
  box-shadow: var(--energy-pulse);
}

/* 优雅紫主题 - VIP烫金效果 */
[data-theme="info"] .badge-primary {
  background: var(--vip-gradient);
  box-shadow: var(--gold-glow);
  animation: goldShimmer 3s ease-in-out infinite;
}

@keyframes goldShimmer {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2); }
}

/* 深邃红主题 - 金属光泽效果 */
[data-theme="danger"] .btn-primary {
  background: var(--theme-metallic-sheen);
  color: var(--theme-text-on-primary);
}

/* 用友主题 - 专业阴影效果 */
[data-theme="yonyou"] .btn-primary:focus {
  box-shadow: var(--yonyou-shadow);
}

[data-theme="yonyou"] .navbar {
  box-shadow: 0 2px 8px rgba(30, 136, 229, 0.15);
}

[data-theme="yonyou"] .table {
  border: 1px solid #E0E0E0;
}

[data-theme="yonyou"] .table th {
  background-color: #E3F2FD;
  color: #1565C0;
  border: 1px solid #BBDEFB;
}

[data-theme="yonyou"] .table tbody tr:hover {
  background-color: var(--theme-hover);
}

/* 复古典雅风 - 移除模糊效果，保持清晰 */
[data-theme="vintage-elegant"] .card {
  font-family: var(--serif-font);
  background-color: var(--theme-surface);
}

[data-theme="vintage-elegant"] .btn {
  /* 移除文字阴影，保持文字清晰 */
  text-shadow: none;
  font-weight: 600; /* 增加字重提高可读性 */
}

/* === 响应式动画优化 === */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* === 性能优化 === */
.card,
.btn,
.navbar {
  will-change: transform, box-shadow;
}

/* === 主题切换动画 === */
[data-theme] {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === 季度色彩偏好调研提示 === */
.theme-feedback-prompt {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--theme-primary);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

[data-theme="dark"] .navbar {
  background-color: var(--theme-primary) !important;
}

[data-theme="dark"] .table {
  color: #fff;
}

[data-theme="yonyou"] .form-control {
  background-color: #FFFFFF;
  border-color: #E0E0E0;
  color: #212121;
  border-radius: 4px;
}

[data-theme="yonyou"] .form-control:focus {
  background-color: #FFFFFF;
  border-color: var(--theme-primary);
  color: #212121;
  box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

[data-theme="yonyou"] .btn {
  border-radius: 4px;
  font-weight: 500;
}

[data-theme="yonyou"] .btn-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

[data-theme="yonyou"] .btn-primary:hover {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-nav .nav-link.active {
    border-radius: 0.25rem;
    margin: 0.25rem;
  }
}

/* 主题切换按钮样式 */
.theme-selector {
  position: relative;
}

.theme-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  vertical-align: middle;
}

/* 主题预览颜色 - 与实际主题配色完全匹配 */
.theme-preview.primary {
  background: linear-gradient(135deg, #001F3F 0%, #001122 100%);
  box-shadow: 0 2px 4px rgba(0, 31, 63, 0.3);
}
.theme-preview.secondary {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  box-shadow: 0 2px 4px rgba(51, 51, 51, 0.3);
}
.theme-preview.success {
  background: linear-gradient(135deg, #4A7856 0%, #2d4a35 100%);
  box-shadow: 0 2px 4px rgba(74, 120, 86, 0.3);
}
.theme-preview.warning {
  background: linear-gradient(135deg, #FF6B35 0%, #e55a2b 100%);
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}
.theme-preview.info {
  background: linear-gradient(135deg, #B399D4 0%, #7851A9 100%);
  box-shadow: 0 2px 4px rgba(179, 153, 212, 0.3);
}
.theme-preview.danger {
  background: linear-gradient(135deg, #C91F37 0%, #900020 100%);
  box-shadow: 0 2px 4px rgba(201, 31, 55, 0.3);
}
.theme-preview.yonyou {
  background: linear-gradient(135deg, #1E88E5 0%, #1565C0 100%);
  box-shadow: 0 2px 4px rgba(30, 136, 229, 0.3);
}

/* 经典配色方案预览 - 与实际主题配色完全匹配 */
.theme-preview.classic-neutral {
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  box-shadow: 0 2px 4px rgba(139, 69, 19, 0.3);
}
.theme-preview.modern-neutral {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 2px 4px rgba(74, 85, 104, 0.3);
}
.theme-preview.vintage-elegant {
  background: linear-gradient(135deg, #004225 0%, #002818 100%);
  box-shadow: 0 2px 4px rgba(0, 66, 37, 0.3);
}
.theme-preview.noble-elegant {
  background: linear-gradient(135deg, #191970 0%, #0f0f4d 100%);
  box-shadow: 0 2px 4px rgba(25, 25, 112, 0.3);
}
.theme-preview.fresh-elegant {
  background: linear-gradient(135deg, #4682b4 0%, #2f4f4f 100%);
  box-shadow: 0 2px 4px rgba(70, 130, 180, 0.3);
}
.theme-preview.spring-fresh {
  background: linear-gradient(135deg, #228b22 0%, #006400 100%);
  box-shadow: 0 2px 4px rgba(34, 139, 34, 0.3);
}
.theme-preview.luxury-solemn {
  background: linear-gradient(135deg, #ffd700 0%, #b8860b 100%);
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}
.theme-preview.royal-solemn {
  background: linear-gradient(135deg, #4b0082 0%, #301934 100%);
  box-shadow: 0 2px 4px rgba(75, 0, 130, 0.3);
}

/* 主题切换器下拉菜单样式 */
#themeDropdown {
  color: rgba(255, 255, 255, 0.9) !important;
  transition: color 0.3s ease;
}

#themeDropdown:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.25rem;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  text-decoration: none;
}

.theme-option.active {
  background-color: var(--theme-primary);
  color: white;
}

.theme-option.active .theme-preview {
  border-color: white;
}

/* 主题切换器在系统设置页面的样式 */
.theme-color-selector {
  position: relative;
}

.theme-color-selector option {
  padding: 0.5rem;
}

/* 主题切换动画增强 */
.navbar-nav .nav-link {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.dropdown-menu {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 主题预览提示样式 */
.theme-preview-toast {
  background: linear-gradient(45deg, var(--theme-primary), var(--theme-primary-light));
  color: white;
  border: none;
}

/* 响应式主题切换器 */
@media (max-width: 768px) {
  .theme-preview {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  .theme-option {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}
