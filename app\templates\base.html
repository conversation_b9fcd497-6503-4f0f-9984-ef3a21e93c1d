<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="{{ theme_color|default('primary') }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂(Scmmp) ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/toastr/css/toastr.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/jquery-ui/css/jquery-ui.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/datatables/css/dataTables.bootstrap4.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/select2/select2.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=2.4.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-optimization.css') }}?v=2.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard-optimization.css') }}?v=1.5.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-image-uploader.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/file-upload-fix.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/inventory-optimization.css') }}?v=1.0.0">

    <!-- 自定义 CSP 修复样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom-csp-fixes.css') }}?v=1.0.0">
    <!-- 优雅导航样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/elegant-navigation.css') }}?v=1.0.0">
    <!-- 移动端优化样式 - 最后加载确保优先级 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-optimization.css') }}?v=1.1.0">
    {% block styles %}{% endblock %}
    <style>
        /* 导航栏LOGO样式 */
        .navbar-logo {
            transition: all 0.3s ease;
        }
        .navbar-logo:hover {
            transform: scale(1.05);
        }
        .navbar-brand-text {
            white-space: nowrap;
        }

        .navbar-brand-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            line-height: 1.2;
        }

        .navbar-school-name {
            font-size: 0.95rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.95);
            white-space: nowrap;
            margin-top: 6px;
            letter-spacing: 0.3px;
            padding: 2px 8px;
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .navbar-logo {
                height: 32px !important;
                max-width: 100px !important;
            }
            .navbar-brand-text {
                font-size: 0.9rem;
            }
            .navbar-school-name {
                font-size: 0.75rem;
            }
        }

        /* 超小屏幕只显示LOGO */
        @media (max-width: 480px) {
            .navbar-brand-text {
                display: none;
            }
            .navbar-school-name {
                display: none;
            }
            .navbar-logo {
                margin-right: 0 !important;
            }
        }
    </style>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/critical-handler-simple.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</head>
<body data-theme="{{ theme_color|default('primary') }}">
    <nav class="navbar navbar-expand-lg navbar-dark bg-{{ theme_color|default('primary') }}">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('main.index') }}">
                {% if system_logo %}
                <img src="{{ system_logo }}" alt="{{ project_name }}" class="navbar-logo me-2" style="height: 40px; max-width: 120px; object-fit: contain;">
                {% endif %}
                <div class="navbar-brand-container">
                    <span class="navbar-brand-text">{{ project_name|default('校园餐智慧食堂平台') }}</span>
                    {% if current_user.is_authenticated and current_user.get_current_area() %}
                    <span class="navbar-school-name">{{ current_user.get_current_area().name }}</span>
                    {% endif %}
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">

                    {% if current_user.is_authenticated %}
                        {% for menu_item in user_menu %}
                            {% if menu_item.children %}
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="{{ menu_item.id }}Dropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i> {% endif %}
                                        {{ menu_item.name }}
                                    </a>
                                    <div class="dropdown-menu">
                                        {% for child in menu_item.children %}
                                            {% if child.get('is_header') %}
                                                <!-- 分组标题 -->
                                                <h6 class="dropdown-header">{{ child.name }}</h6>
                                            {% else %}
                                                <!-- 普通菜单项 -->
                                                <a class="dropdown-item" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}">
                                                    {% if child.icon %}<i class="{{ child.icon }}"></i> {% endif %}
                                                    {{ child.name }}
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                        {% if menu_item.id == 'area' and current_user.area_id %}
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="{{ url_for('area.view_area', id=current_user.area_id) }}">
                                                当前区域: {{ current_user_area_name or '未知区域' }}
                                            </a>
                                        {% endif %}
                                    </div>
                                </li>
                            {% else %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for(menu_item.url) if not menu_item.get('url_params') else get_url(menu_item) }}">
                                        {% if menu_item.icon %}<i class="{{ menu_item.icon }}"></i> {% endif %}
                                        {{ menu_item.name }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <!-- 主题切换器 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="themeDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="切换主题">
                            <i class="fas fa-palette"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="themeDropdown" style="max-height: 400px; overflow-y: auto;">
                            <h6 class="dropdown-header">🎨 现代专业系列</h6>
                            <a class="dropdown-item theme-option" href="#" data-theme="primary">
                                <span class="theme-preview primary"></span>🌊 海洋蓝主题
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="secondary">
                                <span class="theme-preview secondary"></span>🔘 现代灰主题
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="success">
                                <span class="theme-preview success"></span>🌿 自然绿主题
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="warning">
                                <span class="theme-preview warning"></span>🔥 活力橙主题
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="info">
                                <span class="theme-preview info"></span>💜 优雅紫主题
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="danger">
                                <span class="theme-preview danger"></span>❤️ 深邃红主题
                            </a>
                            

                            <div class="dropdown-divider"></div>
                            <h6 class="dropdown-header">✨ 经典优雅系列</h6>
                            <a class="dropdown-item theme-option" href="#" data-theme="classic-neutral">
                                <span class="theme-preview classic-neutral"></span>🏛️ 经典中性风
                            </a>
                            <a class="dropdown-item theme-option" href="#" data-theme="modern-neutral">
                                <span class="theme-preview modern-neutral"></span>🏢 现代中性风
                            </a>
                                                        <a class="dropdown-item theme-option" href="#" data-theme="noble-elegant">
                                <span class="theme-preview noble-elegant"></span>👑 贵族典雅风
                            </a>
                                                                                <a class="dropdown-item theme-option" href="#" data-theme="royal-solemn">
                                <span class="theme-preview royal-solemn"></span>🎭 皇室庄重风
                            </a>


                        </div>
                    </li>
                    <!-- 通知图标和下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link" href="#" id="notificationDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if current_user.unread_notifications_count > 0 %}
                            <span class="badge badge-danger notification-badge" id="notification-badge">{{ current_user.unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right notification-dropdown" aria-labelledby="notificationDropdown">
                            <h6 class="dropdown-header">通知中心</h6>
                            <div id="notification-list">
                                {% if current_user.recent_notifications %}
                                    {% for notification in current_user.recent_notifications %}
                                    <a class="dropdown-item notification-item {% if not notification['is_read'] %}unread{% endif %}" href="{{ url_for('notification.view', id=notification['id']) }}">
                                        <div class="notification-title">
                                            {% if notification['level'] == 2 %}
                                            <span class="badge badge-danger">紧急</span>
                                            {% elif notification['level'] == 1 %}
                                            <span class="badge badge-warning">重要</span>
                                            {% endif %}
                                            {{ notification['title'] }}
                                        </div>
                                        <div class="notification-content">{{ notification['content']|truncate(50) }}</div>
                                        <div class="notification-time">
                                            {% if notification['created_at'] is string %}
                                                {{ notification['created_at'] }}
                                            {% else %}
                                                {{ notification['created_at']|format_datetime }}
                                            {% endif %}
                                        </div>
                                    </a>
                                    {% endfor %}
                                {% else %}
                                <div class="dropdown-item text-center">暂无通知</div>
                                {% endif %}
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center" href="{{ url_for('notification.index') }}">查看全部通知</a>
                            {% if current_user.unread_notifications_count > 0 %}
                            <a class="dropdown-item text-center" href="{{ url_for('notification.mark_all_read') }}">全部标为已读</a>
                            {% endif %}
                        </div>
                    </li>
                    <!-- 用户下拉菜单 -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            {{ current_user.username }}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#">
                                <i class="fas fa-user"></i> 个人资料
                            </a>
                            <a class="dropdown-item" href="{{ url_for('help.index') }}">
                                <i class="fas fa-question-circle"></i> 帮助中心
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a>
                        </div>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.login') }}">登录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('auth.register') }}">注册</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div class="alert alert-{{ category }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">{{ project_name|default('校园餐智慧食堂(Scmmp) ') }} &copy; {{ now.year }}</span>
        </div>
    </footer>

    <!-- 首先加载 jQuery -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>

    <!-- 事件处理器管理器（必须在其他事件处理器之前加载） -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/event-handler-manager.js') }}"></script>

    <!-- 其他脚本 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/csp-helper.js') }}"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/jquery-ui-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/datepicker-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/datatables-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/form-validation-zh-CN.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/i18n.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mock-api-handler.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/main.js') }}?v=1.0.2"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/theme-switcher.js') }}?v=1.0.3"></script>
    <!-- 移动端优化后的主题功能 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/advanced-theme-features.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/auth-helper.js') }}?v=1.0.1"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/enhanced-image-uploader.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.2"></script>
    <!-- 移动端表格转卡片工具 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-table-cards.js') }}?v=1.0.0"></script>
    <!-- 移动端增强功能 -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-enhancements.js') }}?v=2.0.0"></script>
    <!-- 简化的移动端优化 -->
    <script nonce="{{ csp_nonce }}">
        $(document).ready(function() {
            // 简单的移动端检测和优化
            function isMobile() {
                return window.innerWidth <= 768;
            }

            // 移动端导航优化
            if (isMobile()) {
                // 确保移动端下拉菜单正常工作
                $('.navbar-nav .dropdown-toggle').on('click', function(e) {
                    if (isMobile()) {
                        e.preventDefault();
                        var $dropdown = $(this).closest('.dropdown');
                        var $menu = $dropdown.find('.dropdown-menu');

                        // 切换显示状态
                        $dropdown.toggleClass('show');
                        $menu.toggleClass('show');
                        $(this).attr('aria-expanded', $dropdown.hasClass('show'));
                    }
                });

                // 点击菜单项后关闭菜单
                $('.navbar-nav .dropdown-item').on('click', function() {
                    if (isMobile()) {
                        $('.navbar-collapse').collapse('hide');
                    }
                });
            }

            // 优化触摸目标大小
            if (isMobile() && 'ontouchstart' in window) {
                $('.btn, .nav-link, .dropdown-item').each(function() {
                    var $this = $(this);
                    if ($this.height() < 44) {
                        $this.css({
                            'min-height': '44px',
                            'display': 'flex',
                            'align-items': 'center'
                        });
                    }
                });
            }
        });
    </script>

    <!-- 临时递归修复脚本 -->
    <script nonce="{{ csp_nonce }}">
        // 检测到递归错误时自动修复
        window.addEventListener('error', function(e) {
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                console.error('检测到递归错误，正在自动修复...');

                // 加载快速修复脚本
                const script = document.createElement('script');
                script.src = '/static/js/quick_fix_recursion.js?v=' + Date.now();
                script.onload = function() {
                    console.log('递归修复脚本已加载');
                };
                document.head.appendChild(script);
            }
        });
    </script>
    <script nonce="{{ csp_nonce }}">
        // 配置toastr通知
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // 初始化本地化设置
        $(document).ready(function() {
            // 设置 moment.js 的语言
            moment.locale('zh-CN');

            // 设置 bootstrap-table 的默认选项
            $.extend($.fn.bootstrapTable.defaults, {
                locale: 'zh-CN',
                formatLoadingMessage: function() {
                    return '正在加载中...';
                }
            });
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
