/* 
 * 响应式断点统一配置
 * 定义全局响应式断点和通用类
 */

/* === CSS变量定义 === */
:root {
    /* 断点定义 */
    --breakpoint-xs: 480px;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
    
    /* 字体大小 */
    --font-size-xs: 0.65rem;
    --font-size-sm: 0.75rem;
    --font-size-base: 0.875rem;
    --font-size-lg: 1rem;
    --font-size-xl: 1.125rem;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-xxl: 24px;
    
    /* 触摸目标最小尺寸 */
    --touch-target-min: 44px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* === 响应式显示控制类 === */
.desktop-only {
    display: block;
}

.mobile-only {
    display: none;
}

.tablet-only {
    display: none;
}

/* === 移动端 (≤768px) === */
@media (max-width: 768px) {
    .desktop-only {
        display: none !important;
    }
    
    .mobile-only {
        display: block !important;
    }
    
    .tablet-only {
        display: none !important;
    }
    
    /* 移动端字体调整 */
    .mobile-font-sm {
        font-size: var(--font-size-sm) !important;
    }
    
    .mobile-font-xs {
        font-size: var(--font-size-xs) !important;
    }
    
    /* 移动端间距调整 */
    .mobile-p-sm {
        padding: var(--spacing-sm) !important;
    }
    
    .mobile-m-sm {
        margin: var(--spacing-sm) !important;
    }
    
    /* 移动端按钮调整 */
    .mobile-btn-block {
        display: block !important;
        width: 100% !important;
        margin-bottom: var(--spacing-sm) !important;
    }
    
    /* 移动端表格调整 */
    .mobile-table-responsive {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
    }
}

/* === 平板端 (769px - 1024px) === */
@media (min-width: 769px) and (max-width: 1024px) {
    .desktop-only {
        display: block !important;
    }
    
    .mobile-only {
        display: none !important;
    }
    
    .tablet-only {
        display: block !important;
    }
    
    /* 平板端字体调整 */
    .tablet-font-adjust {
        font-size: var(--font-size-base) !important;
    }
}

/* === 小屏幕移动端 (≤480px) === */
@media (max-width: 480px) {
    /* 超小屏幕字体调整 */
    .xs-font-tiny {
        font-size: var(--font-size-xs) !important;
    }
    
    /* 超小屏幕间距调整 */
    .xs-p-xs {
        padding: var(--spacing-xs) !important;
    }
    
    .xs-m-xs {
        margin: var(--spacing-xs) !important;
    }
    
    /* 超小屏幕按钮调整 */
    .xs-btn-sm {
        padding: 2px 4px !important;
        font-size: var(--font-size-xs) !important;
        min-width: 20px !important;
        height: 18px !important;
    }
}

/* === 大屏幕 (≥1200px) === */
@media (min-width: 1200px) {
    .desktop-only {
        display: block !important;
    }
    
    .mobile-only {
        display: none !important;
    }
    
    .tablet-only {
        display: none !important;
    }
    
    /* 大屏幕字体调整 */
    .lg-font-xl {
        font-size: var(--font-size-xl) !important;
    }
    
    /* 大屏幕容器调整 */
    .lg-container-wide {
        max-width: 1400px !important;
    }
}

/* === 触摸设备优化 === */
@media (hover: none) and (pointer: coarse) {
    /* 触摸设备按钮最小尺寸 */
    .btn,
    .btn-sm,
    .btn-xs {
        min-height: var(--touch-target-min) !important;
        min-width: var(--touch-target-min) !important;
    }
    
    /* 触摸设备链接间距 */
    .nav-link,
    .dropdown-item {
        min-height: var(--touch-target-min) !important;
        display: flex !important;
        align-items: center !important;
    }
    
    /* 触摸设备表单控件 */
    .form-control,
    .form-select {
        min-height: var(--touch-target-min) !important;
        font-size: 16px !important; /* 防止iOS缩放 */
    }
}

/* === 打印媒体查询 === */
@media print {
    .desktop-only,
    .mobile-only,
    .tablet-only {
        display: block !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    /* 打印字体调整 */
    body {
        font-size: 12pt !important;
        line-height: 1.4 !important;
    }
    
    /* 打印间距调整 */
    .print-compact {
        margin: 0 !important;
        padding: 2px !important;
    }
}

/* === 高对比度模式 === */
@media (prefers-contrast: high) {
    :root {
        --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.3);
        --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
    
    .btn,
    .card,
    .table {
        border-width: 2px !important;
    }
}

/* === 减少动画模式 === */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* === 暗色模式支持 === */
@media (prefers-color-scheme: dark) {
    :root {
        --shadow-sm: 0 1px 3px rgba(255, 255, 255, 0.1);
        --shadow-md: 0 2px 8px rgba(255, 255, 255, 0.1);
        --shadow-lg: 0 4px 16px rgba(255, 255, 255, 0.1);
    }
}

/* === 工具类 === */
.responsive-text {
    font-size: var(--font-size-base);
}

.responsive-spacing {
    padding: var(--spacing-md);
    margin: var(--spacing-md);
}

.responsive-shadow {
    box-shadow: var(--shadow-sm);
}

.responsive-border-radius {
    border-radius: 6px;
}

/* === 容器响应式 === */
.responsive-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
    .responsive-container {
        padding: 0 var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .responsive-container {
        padding: 0 var(--spacing-xs);
    }
}
