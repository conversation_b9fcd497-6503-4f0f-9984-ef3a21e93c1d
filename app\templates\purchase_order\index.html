{% extends 'base.html' %}

{% block title %}采购订单列表 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 表格按钮优化 */
.table-actions {
    white-space: nowrap;
}

.table-actions a,
.table-actions button {
    display: inline-block;
    padding: 2px 6px;
    margin: 0 1px;
    font-size: 11px;
    line-height: 1.4;
    border: none;
    border-radius: 3px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.table-actions .btn-outline-primary {
    background-color: #007bff;
    color: white;
}

.table-actions .btn-outline-success {
    background-color: #28a745;
    color: white;
}

.table-actions .btn-outline-danger {
    background-color: #dc3545;
    color: white;
}

.table-actions .btn-outline-info {
    background-color: #17a2b8;
    color: white;
}

.table-actions .btn-outline-secondary {
    background-color: #6c757d;
    color: white;
}

.table-actions a:hover,
.table-actions button:hover {
    opacity: 0.8;
    transform: translateY(-1px);
    text-decoration: none;
    color: inherit;
}

/* 表格列宽优化 */
.table th,
.table td {
    vertical-align: middle;
    padding: 6px;
    font-size: 12px;
}

.order-number-column { width: 140px; }
.datetime-column {
    width: 80px;
    font-size: 11px;
}
.amount-column {
    width: 80px;
    text-align: right;
}
.status-column {
    width: 120px;
    font-size: 11px;
}
.action-column {
    width: 280px;
    min-width: 280px;
}

/* 状态标签优化 */
.status-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 2px;
}

.badge {
    font-size: 9px;
    padding: 1px 4px;
}

/* 金额显示优化 */
.amount-display {
    font-weight: 500;
    color: #28a745;
}

/* 日期时间显示优化 */
.datetime-display {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.datetime-display .date {
    font-weight: 500;
}

.datetime-display .time {
    color: #6c757d;
    font-size: 10px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .table-actions a,
    .table-actions button {
        font-size: 10px;
        padding: 1px 4px;
        margin: 0;
    }

    .action-column {
        min-width: 220px;
    }
}

/* 表格整体优化 */
.table-responsive {
    border-radius: 6px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 12px;
    color: #495057;
    text-align: center;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.order-status {
    font-size: 0.85em;
    padding: 0.25em 0.6em;
    border-radius: 0.25rem;
}
.status-pending, .status-待确认 {
    background-color: #ffc107;
    color: #000;
}
.status-confirmed, .status-已确认 {
    background-color: #17a2b8;
    color: #fff;
}
.status-delivered, .status-已送达 {
    background-color: #28a745;
    color: #fff;
}
.status-cancelled, .status-已取消 {
    background-color: #dc3545;
    color: #fff;
}

/* 状态更新动画效果 */
.table-success {
    background-color: #d4edda !important;
    transition: background-color 0.3s ease;
}

/* 消息提示样式 */
.alert-message {
    position: relative;
    z-index: 1050;
    margin-bottom: 1rem;
}

.alert-message .fas {
    margin-right: 0.5rem;
}

/* 按钮状态样式 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 状态徽章样式 */
.status-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.status-warning {
    background-color: #ffc107;
    color: #212529;
}

.status-info {
    background-color: #17a2b8;
    color: #fff;
}

.status-success {
    background-color: #28a745;
    color: #fff;
}

.status-danger {
    background-color: #dc3545;
    color: #fff;
}

/* 行淡出动画 */
.fade-out {
    opacity: 0;
    transition: opacity 0.5s ease;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2>采购订单管理</h2>
        </div>
        <div class="col-12 mt-2">
            <!-- 移动端按钮 -->
            <div class="action-buttons d-md-none">
                <button type="button" class="btn btn-primary btn-block" data-toggle="modal" data-target="#createFromMenuModal">
                    <i class="fas fa-plus"></i> 从周菜单创建
                </button>
                <a href="{{ url_for('purchase_order.create_form') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-edit"></i> 手动创建
                </a>
            </div>
            <!-- 桌面端按钮 -->
            <div class="d-none d-md-block text-right">
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createFromMenuModal">
                        <i class="fas fa-plus"></i> 从周菜单创建
                    </button>
                    <a href="{{ url_for('purchase_order.create_form') }}" class="btn btn-outline-primary">
                        <i class="fas fa-edit"></i> 手动创建
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row">
                <div class="col-12 col-md-3 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend d-none d-md-flex">
                            <span class="input-group-text">订单号</span>
                        </div>
                        <input type="text" class="form-control" name="order_number" placeholder="输入订单号搜索">
                    </div>
                </div>
                <div class="col-12 col-md-2 mb-2">
                    <select class="form-control" name="status">
                        <option value="">所有状态</option>
                        <option value="待确认">待确认</option>
                        <option value="已确认">已确认</option>
                        <option value="已送达">已送达</option>
                        <option value="已取消">已取消</option>
                    </select>
                </div>
                <div class="col-12 col-md-3 mb-2">
                    <div class="row">
                        <div class="col-6">
                            <input type="date" class="form-control" name="start_date" placeholder="开始日期">
                        </div>
                        <div class="col-6">
                            <input type="date" class="form-control" name="end_date" placeholder="结束日期">
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-2 mb-2">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i> <span class="d-none d-md-inline">搜索</span>
                    </button>
                </div>
                <div class="col-6 col-md-2 mb-2">
                    <button type="reset" class="btn btn-secondary btn-block">
                        <i class="fas fa-redo"></i> <span class="d-none d-md-inline">重置</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="card">
        <div class="card-body">
            <!-- 表格工具栏 -->
            <div class="table-toolbar">
                <div class="toolbar-left">
                    <h6 class="mb-0">
                        <i class="fas fa-list"></i> 采购订单列表
                        <span class="badge badge-primary ml-2">{{ orders.items|length }} 条记录</span>
                    </h6>
                </div>
                <div class="toolbar-right">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        表格已优化显示，支持紧凑排版
                    </small>
                </div>
            </div>

            <!-- 桌面端表格视图 -->
            <div class="table-responsive d-none d-lg-block">
                <table class="table table-hover table-compact">
                    <thead>
                        <tr>
                            <th class="order-number-column">订单号</th>
                            <th class="datetime-column">创建时间</th>
                            <th class="datetime-column">送货日期</th>
                            <th class="amount-column">总金额</th>
                            <th class="status-column">状态</th>
                            <th class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr class="{% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}table-light{% elif orders_status_info[order.id]['consumption_status'] == '部分消耗' %}table-warning{% endif %}">
                            <td class="order-number-column">
                                <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="text-primary font-weight-medium">
                                    {{ order.order_number }}
                                </a>
                            </td>
                            <td class="datetime-column">
                                <div class="datetime-display">
                                    <span class="date">{{ order.order_date|format_datetime('%m-%d') if order.order_date else '-' }}</span>
                                    <span class="time">{{ order.order_date|format_datetime('%H:%M') if order.order_date else '' }}</span>
                                </div>
                            </td>
                            <td class="datetime-column">{{ order.delivery_date|format_datetime('%m-%d') if order.delivery_date else '-' }}</td>
                            <td class="amount-column">
                                <span class="amount-display">¥{{ "%.2f"|format(order.total_amount) }}</span>
                            </td>
                            <td class="status-column">
                                <div class="d-flex flex-column">
                                    <!-- 订单状态 -->
                                    {% if order.status == '待确认' %}
                                    <span class="status-badge status-warning">待确认</span>
                                    {% elif order.status == '已确认' %}
                                    <span class="status-badge status-info">已确认</span>
                                    {% elif order.status == '已送达' %}
                                    <span class="status-badge status-success">已送达</span>
                                    {% elif order.status == '已取消' %}
                                    <span class="status-badge status-danger">已取消</span>
                                    {% else %}
                                    <span class="status-badge">{{ order.get_status_display() }}</span>
                                    {% endif %}

                                    <!-- 入库状态 -->
                                    {% if orders_status_info[order.id]['has_stock_in'] %}
                                        <span class="badge badge-info">
                                            {{ orders_status_info[order.id]['stock_in_status'] }}
                                        </span>
                                    {% else %}
                                        <span class="badge badge-secondary">未入库</span>
                                    {% endif %}

                                    <!-- 消耗状态 -->
                                    {% if orders_status_info[order.id]['consumption_status'] != '未消耗' %}
                                        <span class="badge {% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}badge-success{% else %}badge-warning{% endif %}">
                                            {{ orders_status_info[order.id]['consumption_status'] }}
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="action-column">
                                <div class="table-actions">
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn-outline-primary" title="查看详情">查看</a>

                                    {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                    <!-- 学校管理员或系统管理员可以执行所有操作 -->
                                    <button type="button" class="btn-outline-success confirm-btn" data-id="{{ order.id }}" title="确认订单">确认</button>
                                    <button type="button" class="btn-outline-danger cancel-btn" data-id="{{ order.id }}" title="取消订单">取消</button>
                                    <button type="button" class="btn-outline-info deliver-btn" data-id="{{ order.id }}" title="标记送达">送达</button>
                                    <!-- 删除按钮：只有未入库的订单可以删除 -->
                                    {% if order.id not in stock_in_order_ids and order.status != '已入库' %}
                                    <button type="button" class="btn-outline-danger delete-btn" data-id="{{ order.id }}" title="删除订单">删除</button>
                                    {% endif %}
                                    {% else %}
                                    <!-- 普通用户只能根据状态执行特定操作 -->
                                    {% if order.status == '待确认' %}
                                    <button type="button" class="btn-outline-success confirm-btn" data-id="{{ order.id }}" title="确认订单">确认</button>
                                    <button type="button" class="btn-outline-danger cancel-btn" data-id="{{ order.id }}" title="取消订单">取消</button>
                                    {% endif %}
                                    {% if order.status == '已确认' %}
                                    <button type="button" class="btn-outline-info deliver-btn" data-id="{{ order.id }}" title="标记送达">送达</button>
                                    {% endif %}

                                    <!-- 删除按钮：未入库的订单可以删除 -->
                                    {% if order.status in ['待确认', '已取消'] and order.id not in stock_in_order_ids %}
                                    <button type="button" class="btn-outline-danger delete-btn" data-id="{{ order.id }}" title="删除订单">删除</button>
                                    {% endif %}
                                    {% endif %}

                                    {# 所有状态的订单都可以创建入库单 #}
                                    {% if order.id in stock_in_order_ids %}
                                    <a href="{{ url_for('stock_in.index') }}" class="btn-outline-secondary" title="已创建入库单">已入库</a>
                                    {% else %}
                                    <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn-outline-success" title="一键入库">入库</a>
                                    {% endif %}
                                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn-outline-secondary" target="_blank" title="打印订单">打印</a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="table-empty">
                                <i class="fas fa-inbox"></i>
                                <h5>暂无采购订单</h5>
                                <p>您可以创建新的采购订单或调整筛选条件</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 移动端卡片视图 -->
            <div class="d-lg-none">
                {% for order in orders.items %}
                <div class="card mb-3 border-left-{% if order.status == '待确认' %}warning{% elif order.status == '已确认' %}info{% elif order.status == '已送达' %}success{% elif order.status == '已取消' %}danger{% else %}secondary{% endif %}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <h6 class="card-title mb-2">
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="text-primary">
                                        {{ order.order_number }}
                                    </a>
                                </h6>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">创建时间</small>
                                <div class="small">{{ order.order_date|format_datetime('%m-%d %H:%M') if order.order_date else '-' }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">送货日期</small>
                                <div class="small">{{ order.delivery_date|format_datetime('%m-%d') if order.delivery_date else '-' }}</div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-6">
                                <small class="text-muted">总金额</small>
                                <div class="font-weight-bold text-success">¥{{ "%.2f"|format(order.total_amount) }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">状态</small>
                                <div>
                                    {% if order.status == '待确认' %}
                                    <span class="badge badge-warning">{{ order.get_status_display() }}</span>
                                    {% elif order.status == '已确认' %}
                                    <span class="badge badge-info">{{ order.get_status_display() }}</span>
                                    {% elif order.status == '已送达' %}
                                    <span class="badge badge-success">{{ order.get_status_display() }}</span>
                                    {% elif order.status == '已取消' %}
                                    <span class="badge badge-danger">{{ order.get_status_display() }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ order.get_status_display() }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">入库状态</small>
                                <div>
                                    {% if orders_status_info[order.id]['has_stock_in'] %}
                                    <span class="badge badge-info">{{ orders_status_info[order.id]['stock_in_status'] }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">未入库</span>
                                    {% endif %}

                                    {% if orders_status_info[order.id]['consumption_status'] != '未消耗' %}
                                    <span class="badge {% if orders_status_info[order.id]['consumption_status'] == '已消耗' %}badge-success{% else %}badge-warning{% endif %} ml-1">
                                        {{ orders_status_info[order.id]['consumption_status'] }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="btn-group btn-group-sm w-100" role="group">
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                    <button type="button" class="btn btn-outline-success confirm-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger cancel-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info deliver-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                    {% if order.id not in stock_in_order_ids and order.status != '已入库' %}
                                    <button type="button" class="btn btn-outline-danger delete-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    {% endif %}
                                    {% else %}
                                    {% if order.status == '待确认' %}
                                    <button type="button" class="btn btn-outline-success confirm-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger cancel-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    {% if order.status == '已确认' %}
                                    <button type="button" class="btn btn-outline-info deliver-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                    {% endif %}
                                    {% if order.status in ['待确认', '已取消'] and order.id not in stock_in_order_ids %}
                                    <button type="button" class="btn btn-outline-danger delete-btn" data-id="{{ order.id }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    {% endif %}
                                    {% endif %}

                                    {% if order.id in stock_in_order_ids %}
                                    <a href="{{ url_for('stock_in.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-check-circle"></i>
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}" class="btn btn-outline-success">
                                        <i class="fas fa-dolly"></i>
                                    </a>
                                    {% endif %}

                                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}" class="btn btn-outline-secondary" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5>暂无采购订单</h5>
                    <p class="text-muted">您可以创建新的采购订单或调整筛选条件</p>
                </div>
                {% endfor %}
            </div>

            <!-- 分页导航 -->
            {% if orders.pages > 1 %}
            <nav aria-label="采购订单分页">
                <ul class="pagination justify-content-center">
                    <!-- 上一页 -->
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.prev_num, **request.args) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </span>
                    </li>
                    {% endif %}

                    <!-- 页码 -->
                    {% for page_num in orders.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('purchase_order.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    <!-- 下一页 -->
                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.next_num, **request.args) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                    {% endif %}
                </ul>

                <!-- 分页信息 -->
                <div class="text-center mt-2">
                    <small class="text-muted">
                        显示第 {{ (orders.page - 1) * orders.per_page + 1 }} - {{ orders.page * orders.per_page if orders.page * orders.per_page <= orders.total else orders.total }} 条，
                        共 {{ orders.total }} 条记录，第 {{ orders.page }} / {{ orders.pages }} 页
                    </small>
                </div>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 从周菜单创建模态框 -->
<div class="modal fade" id="createFromMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择采购区域</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createFromMenuForm">
                    <div class="form-group">
                        <label for="areaSelect">请选择区域：</label>
                        <select class="form-control" id="areaSelect" required>
                            <option value="">请选择...</option>
                            {% for area in areas %}
                            <option value="{{ area.id }}">{{ area.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAreaBtn">
                    <i class="fas fa-arrow-right"></i> 下一步
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 注意：只有未入库的订单才能删除。已入库或已创建入库单的订单无法删除。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    let currentOrderId = null;

    // 初始化日期范围
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    $('input[name="start_date"]').val(thirtyDaysAgo.toISOString().split('T')[0]);
    $('input[name="end_date"]').val(today.toISOString().split('T')[0]);

    // 确认订单
    $('.confirm-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/confirm`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单确认成功');
                    // 更新页面状态而不是重新加载
                    updateOrderStatus(currentOrderId, '已确认', 'status-info');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '已确认');
                } else {
                    showErrorMessage(response.message || '确认订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '确认订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
                $('#confirmModal').modal('hide');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        if (!currentOrderId) return;

        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            showErrorMessage('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/cancel`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单取消成功');
                    // 更新页面状态
                    updateOrderStatus(currentOrderId, '已取消', 'status-danger');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '已取消');
                    // 清空取消原因
                    $('#cancelReason').val('');
                } else {
                    showErrorMessage(response.message || '取消订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '取消订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
                $('#cancelModal').modal('hide');
            }
        });
    });

    // 标记送达
    $('.deliver-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        if (!currentOrderId) return;

        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/deliver`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单已标记为准备入库');
                    // 更新页面状态
                    updateOrderStatus(currentOrderId, '准备入库', 'status-success');
                    // 更新操作按钮
                    updateOrderActions(currentOrderId, '准备入库');
                    // 清空备注
                    $('#deliveryNotes').val('');
                } else {
                    showErrorMessage(response.message || '标记送达失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '标记送达失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
                $('#deliverModal').modal('hide');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/delete`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    // 显示成功消息
                    showSuccessMessage('订单删除成功');
                    // 移除表格行
                    removeOrderRow(currentOrderId);
                } else {
                    showErrorMessage(response.message || '删除订单失败');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.message || '删除订单失败，请重试';
                showErrorMessage(errorMsg);
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });

    // 筛选表单提交
    $('#filterForm').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const params = new URLSearchParams();

        for (const [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        window.location.search = params.toString();
    });

    // 重置筛选
    $('#filterForm button[type="reset"]').click(function() {
        window.location.href = window.location.pathname;
    });

    // 确认区域选择
    $('#confirmAreaBtn').click(function() {
        const areaId = $('#areaSelect').val();
        if (!areaId) {
            alert('请选择区域');
            return;
        }
        window.location.href = '{{ url_for("purchase_order.create_from_menu") }}?area_id=' + areaId;
    });

    // 辅助函数：显示成功消息
    function showSuccessMessage(message) {
        // 移除现有的消息
        $('.alert-message').remove();

        // 创建成功消息
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show alert-message" role="alert">
                <i class="fas fa-check-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // 插入到页面顶部
        $('.container-fluid').prepend(alertHtml);

        // 3秒后自动消失
        setTimeout(function() {
            $('.alert-message').fadeOut();
        }, 3000);
    }

    // 辅助函数：显示错误消息
    function showErrorMessage(message) {
        // 移除现有的消息
        $('.alert-message').remove();

        // 创建错误消息
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show alert-message" role="alert">
                <i class="fas fa-exclamation-circle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;

        // 插入到页面顶部
        $('.container-fluid').prepend(alertHtml);

        // 5秒后自动消失
        setTimeout(function() {
            $('.alert-message').fadeOut();
        }, 5000);
    }

    // 辅助函数：更新订单状态
    function updateOrderStatus(orderId, newStatus, statusClass) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        const statusCell = row.find('td:nth-child(6)'); // 状态列

        // 移除旧的状态类
        const statusBadge = statusCell.find('.status-badge');
        statusBadge.removeClass('status-warning status-info status-success status-danger');

        // 添加新的状态类和文本
        statusBadge.addClass(statusClass).text(newStatus);

        // 添加动画效果
        statusCell.addClass('table-success');
        setTimeout(function() {
            statusCell.removeClass('table-success');
        }, 2000);
    }

    // 辅助函数：更新操作按钮
    function updateOrderActions(orderId, newStatus) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        const actionCell = row.find('.action-column .btn-group');

        // 根据新状态更新按钮显示
        const confirmBtn = actionCell.find('.confirm-btn');
        const cancelBtn = actionCell.find('.cancel-btn');
        const deliverBtn = actionCell.find('.deliver-btn');
        const deleteBtn = actionCell.find('.delete-btn');

        // 隐藏所有状态相关按钮
        confirmBtn.hide();
        cancelBtn.hide();
        deliverBtn.hide();

        // 根据新状态显示相应按钮
        if (newStatus === '待确认') {
            confirmBtn.show();
            cancelBtn.show();
        } else if (newStatus === '已确认') {
            deliverBtn.show();
        }

        // 已取消或已送达的订单不显示状态操作按钮
        if (newStatus === '已取消' || newStatus === '准备入库') {
            // 可以考虑禁用删除按钮或根据业务规则处理
        }
    }

    // 辅助函数：移除订单行
    function removeOrderRow(orderId) {
        const row = $(`tr:has(button[data-id="${orderId}"])`);
        row.fadeOut(500, function() {
            $(this).remove();

            // 检查是否还有订单，如果没有则显示空状态
            const remainingRows = $('tbody tr').length;
            if (remainingRows === 0) {
                const emptyRowHtml = `
                    <tr>
                        <td colspan="7" class="table-empty">
                            <i class="fas fa-inbox"></i>
                            <h5>暂无采购订单</h5>
                            <p>您可以创建新的采购订单或调整筛选条件</p>
                        </td>
                    </tr>
                `;
                $('tbody').html(emptyRowHtml);
            }
        });
    }

    // 页面加载完成后重新绑定事件（用于动态添加的元素）
    function rebindEvents() {
        // 重新绑定确认按钮
        $('.confirm-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#confirmModal').modal('show');
        });

        // 重新绑定取消按钮
        $('.cancel-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#cancelModal').modal('show');
        });

        // 重新绑定送达按钮
        $('.deliver-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#deliverModal').modal('show');
        });

        // 重新绑定删除按钮
        $('.delete-btn').off('click').on('click', function() {
            currentOrderId = $(this).data('id');
            $('#deleteModal').modal('show');
        });
    }

    // 自动刷新功能（可选）
    let autoRefreshInterval;

    function startAutoRefresh() {
        // 每30秒检查一次状态更新
        autoRefreshInterval = setInterval(function() {
            // 只在没有模态框打开时才刷新
            if (!$('.modal').hasClass('show')) {
                checkOrderUpdates();
            }
        }, 30000);
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    function checkOrderUpdates() {
        // 获取当前页面的所有订单ID
        const orderIds = [];
        $('.confirm-btn, .cancel-btn, .deliver-btn').each(function() {
            const orderId = $(this).data('id');
            if (orderId && orderIds.indexOf(orderId) === -1) {
                orderIds.push(orderId);
            }
        });

        if (orderIds.length === 0) return;

        // 发送请求检查状态更新
        $.ajax({
            url: '/purchase-order/check-updates',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ order_ids: orderIds }),
            success: function(response) {
                if (response.success && response.updates) {
                    // 处理状态更新
                    response.updates.forEach(function(update) {
                        if (update.status_changed) {
                            updateOrderStatus(update.order_id, update.new_status, getStatusClass(update.new_status));
                            updateOrderActions(update.order_id, update.new_status);

                            // 显示更新通知
                            showSuccessMessage(`订单 ${update.order_number} 状态已更新为：${update.new_status}`);
                        }
                    });
                }
            },
            error: function() {
                // 静默处理错误，不影响用户体验
                console.log('检查订单更新失败');
            }
        });
    }

    function getStatusClass(status) {
        const statusClassMap = {
            '待确认': 'status-warning',
            '已确认': 'status-info',
            '准备入库': 'status-success',
            '已送达': 'status-success',
            '已取消': 'status-danger'
        };
        return statusClassMap[status] || 'status-warning';
    }

    // 页面可见性API - 当页面不可见时停止自动刷新
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            stopAutoRefresh();
        } else {
            startAutoRefresh();
        }
    });

    // 启动自动刷新（可选功能，用户可以通过设置开启/关闭）
    // startAutoRefresh();
});
</script>
{% endblock %}
