<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 暗夜霓红首页</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            letter-spacing: 0.02em;
            overflow-x: hidden;
            transition: background 0.3s, color 0.3s;
        }
        body.light-theme {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d0036;
        }
        .neon {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            animation: neon-glow 2.5s infinite alternate;
        }
        .light-theme .neon {
            color: #784ba0;
            text-shadow: 0 0 12px #784ba0, 0 0 32px #ff3cac;
            animation: neon-glow-light 2.5s infinite alternate;
        }
        @keyframes neon-glow {
            0% { text-shadow: 0 0 8px #ff3cac, 0 0 16px #784ba0; }
            100% { text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0; }
        }
        @keyframes neon-glow-light {
            0% { text-shadow: 0 0 8px #784ba0, 0 0 16px #ff3cac; }
            100% { text-shadow: 0 0 24px #784ba0, 0 0 48px #ff3cac; }
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .navbar {
            background: rgba(30,0,50,0.95);
            box-shadow: 0 2px 24px #ff3cac55;
            padding: 18px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 0 0 18px 18px;
            margin-bottom: 18px;
            transition: background 0.3s, box-shadow 0.3s;
        }
        .light-theme .navbar {
            background: rgba(255,255,255,0.95);
            box-shadow: 0 2px 24px #784ba055;
        }
        .navbar-brand {
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            letter-spacing: 2px;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .icon-svg {
            display: inline-flex;
            vertical-align: middle;
            margin-right: 8px;
            filter: drop-shadow(0 0 6px #ff3cac88);
        }
        .navbar-nav {
            display: flex;
            gap: 32px;
        }
        .navbar-nav a {
            color: #fff;
            font-size: 1.1rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s, text-shadow 0.2s;
            text-shadow: 0 0 4px #784ba0;
        }
        .navbar-nav a:hover {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac;
        }
        .navbar-actions {
            display: flex;
            gap: 16px;
        }
        .btn-neon {
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 12px 32px;
            font-size: 1.1rem;
            font-weight: 700;
            box-shadow: 0 0 18px #ff3cac99, 0 0 4px #fff2;
            transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
            position: relative;
            overflow: hidden;
        }
        .btn-neon:after {
            content: '';
            position: absolute;
            left: -50%;
            top: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, #ff3cac55 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .btn-neon:hover {
            background: linear-gradient(90deg, #784ba0 0%, #ff3cac 100%);
            box-shadow: 0 0 32px #ff3caccc, 0 0 8px #fff4;
            transform: scale(1.04);
        }
        .btn-neon:hover:after {
            opacity: 0.3;
        }
        .hero {
            text-align: center;
            padding: 120px 0 60px 0;
            position: relative;
        }
        .hero::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 80vw;
            height: 100%;
            background: radial-gradient(circle at 50% 30%, #ff3cac33 0%, transparent 80%);
            z-index: 0;
        }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 18px;
            color: #fff;
            text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0;
            position: relative;
            z-index: 1;
        }
        .hero-desc {
            font-size: 1.3rem;
            color: #ffb6e6;
            margin-bottom: 36px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }
        .hero-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
            position: relative;
            z-index: 1;
        }
        .divider {
            width: 100px;
            height: 4px;
            margin: 32px auto 0 auto;
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            border-radius: 2px;
            box-shadow: 0 0 12px #ff3cac99;
        }
        .features-section {
            margin: 60px 0 40px 0;
        }
        .features-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }
        .feature-card {
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22, 0 0 0 2px #ff3cac33 inset;
            padding: 40px 28px 32px 28px;
            text-align: center;
            color: #fff;
            transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
            position: relative;
            overflow: hidden;
        }
        .light-theme .feature-card {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
            box-shadow: 0 4px 32px #784ba022, 0 0 0 2px #784ba033 inset;
            color: #2d0036;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.04);
            box-shadow: 0 8px 48px #ff3cac77, 0 0 0 2px #ff3cac99 inset;
        }
        .light-theme .feature-card:hover {
            box-shadow: 0 8px 48px #784ba077, 0 0 0 2px #784ba099 inset;
        }
        .feature-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 18px;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        .feature-desc {
            color: #ffb6e6;
            font-size: 1rem;
        }
        .light-theme .feature-desc {
            color: #784ba0;
        }
        .contact-section {
            margin: 60px 0 0 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            padding: 48px 24px;
            color: #fff;
            position: relative;
            transition: background 0.3s;
        }
        .light-theme .contact-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .contact-title {
            font-size: 2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-align: center;
            text-shadow: 0 0 12px #ff3cac;
        }
        .contact-info {
            text-align: center;
            color: #ffb6e6;
            font-size: 1.1rem;
            margin-bottom: 18px;
        }
        .light-theme .contact-info {
            color: #784ba0;
        }
        .contact-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
        }
        .footer {
            text-align: center;
            color: #ffb6e6;
            padding: 32px 0 12px 0;
            font-size: 1rem;
            margin-top: 60px;
            letter-spacing: 1px;
        }
        .light-theme .footer {
            color: #784ba0;
        }
        /* 粒子背景装饰 */
        .particles {
            position: fixed;
            left: 0; top: 0; width: 100vw; height: 100vh;
            pointer-events: none;
            z-index: 0;
        }
        /* 视频教学区域样式 */
        .tutorial-section {
            margin: 60px 0;
            padding: 40px 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            transition: background 0.3s;
        }
        .light-theme .tutorial-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .tutorial-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 32px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .tutorial-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        .tutorial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        .tutorial-grid.active {
            opacity: 1;
            transform: translateX(0);
        }
        .tutorial-card {
            background: rgba(45, 0, 54, 0.6);
            border-radius: 16px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
            cursor: pointer;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .light-theme .tutorial-card {
            background: rgba(255, 255, 255, 0.6);
        }
        .tutorial-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 32px #ff3cac55;
        }
        .tutorial-thumbnail {
            width: 100%;
            height: 200px;
            background: #1a0025;
            position: relative;
            overflow: hidden;
        }
        .tutorial-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        .tutorial-card:hover .tutorial-thumbnail img {
            transform: scale(1.1);
        }
        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 48px;
            height: 48px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.3s, background-color 0.3s;
        }
        .tutorial-card:hover .play-icon {
            transform: translate(-50%, -50%) scale(1.1);
            background: rgba(255, 60, 172, 1);
        }
        .tutorial-info {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .tutorial-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .tutorial-desc {
            font-size: 0.95rem;
            color: #ffb6e6;
            line-height: 1.5;
            flex-grow: 1;
        }
        .light-theme .tutorial-desc {
            color: #784ba0;
        }
        .tutorial-duration {
            font-size: 0.85rem;
            color: #ff3cac;
            margin-top: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .tutorial-duration svg {
            width: 16px;
            height: 16px;
        }
        .tutorial-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            margin-top: 32px;
        }
        .tutorial-nav-btn {
            background: rgba(255, 60, 172, 0.2);
            border: 2px solid #ff3cac;
            color: #ff3cac;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-btn:hover {
            background: rgba(255, 60, 172, 0.4);
            transform: scale(1.1);
        }
        .tutorial-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .tutorial-nav-dots {
            display: flex;
            gap: 8px;
        }
        .tutorial-nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 60, 172, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-dot.active {
            background: #ff3cac;
            transform: scale(1.2);
        }
        @media (max-width: 1200px) {
            .tutorial-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 768px) {
            .tutorial-grid {
                grid-template-columns: 1fr;
            }
            .tutorial-thumbnail {
                height: 180px;
            }
        }
        /* 视频模态框样式 */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .video-modal.active {
            display: flex;
            opacity: 1;
        }
        .video-container {
            position: relative;
            width: 90%;
            max-width: 1000px;
            margin: auto;
            background: #1a0025;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 0 48px #ff3cac55;
        }
        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
        }
        .close-modal {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.2s;
        }
        .close-modal:hover {
            transform: scale(1.1);
        }
        .theme-toggle {
            background: none;
            border: none;
            color: #ff3cac;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            position: relative;
        }
        .theme-toggle:hover {
            background: rgba(255, 60, 172, 0.1);
            transform: scale(1.1);
        }
        .theme-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background: rgba(45, 0, 54, 0.95);
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
            display: none;
            z-index: 1000;
            min-width: 220px;
            backdrop-filter: blur(10px);
            transform-origin: top left;
            margin-top: 8px;
        }
        .theme-dropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 24px;
            width: 16px;
            height: 16px;
            background: rgba(45, 0, 54, 0.95);
            transform: rotate(45deg);
            border-radius: 2px;
        }
        .theme-dropdown.active {
            display: block;
            animation: dropdown-fade 0.3s ease;
        }
        @keyframes dropdown-fade {
            from { 
                opacity: 0; 
                transform: translateY(-10px) scale(0.95);
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1);
            }
        }
        .theme-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #fff;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .theme-option::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 60, 172, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .theme-option:hover::before {
            opacity: 1;
        }
        .theme-option.active {
            background: rgba(255, 60, 172, 0.2);
        }
        .theme-option.active::before {
            opacity: 1;
        }
        .theme-preview {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 12px rgba(255, 60, 172, 0.2);
            transition: all 0.3s;
        }
        .theme-option:hover .theme-preview {
            transform: scale(1.1);
            box-shadow: 0 0 16px rgba(255, 60, 172, 0.4);
        }
        .theme-name {
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .theme-option.active .theme-name {
            color: #ff3cac;
        }
        /* 主题样式 */
        body.theme-dark {
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
        }
        body.theme-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d0036;
        }
        body.theme-ocean {
            background: linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%);
            color: #fff;
        }
        body.theme-forest {
            background: linear-gradient(135deg, #1a3a2a 0%, #2c503e 100%);
            color: #fff;
        }
        body.theme-sunset {
            background: linear-gradient(135deg, #3a1a2a 0%, #502c3e 100%);
            color: #fff;
        }
        /* 主题预览颜色 */
        .theme-preview.dark { background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%); }
        .theme-preview.light { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); }
        .theme-preview.ocean { background: linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%); }
        .theme-preview.forest { background: linear-gradient(135deg, #1a3a2a 0%, #2c503e 100%); }
        .theme-preview.sunset { background: linear-gradient(135deg, #3a1a2a 0%, #502c3e 100%); }

        /* DeepSeek 现代主题系列 */
        body.theme-deep-sea-tech {
            background: linear-gradient(135deg, #2563EB 0%, #8B5CF6 100%);
            color: #fff;
        }
        body.theme-soft-morandi {
            background: linear-gradient(135deg, #6D28D9 0%, #EC4899 100%);
            color: #fff;
        }
        body.theme-minimal-dawn {
            background: linear-gradient(135deg, #F97316 0%, #EF4444 100%);
            color: #fff;
        }
        body.theme-dark-neon {
            background: linear-gradient(135deg, #8B5CF6 0%, #F43F5E 100%);
            color: #fff;
        }
        body.theme-nature-eco {
            background: linear-gradient(135deg, #10B981 0%, #0EA5E9 100%);
            color: #fff;
        }

        /* DeepSeek 主题预览 */
        .theme-preview.deep-sea-tech { background: linear-gradient(135deg, #2563EB 0%, #8B5CF6 100%); }
        .theme-preview.soft-morandi { background: linear-gradient(135deg, #6D28D9 0%, #EC4899 100%); }
        .theme-preview.minimal-dawn { background: linear-gradient(135deg, #F97316 0%, #EF4444 100%); }
        .theme-preview.dark-neon { background: linear-gradient(135deg, #8B5CF6 0%, #F43F5E 100%); }
        .theme-preview.nature-eco { background: linear-gradient(135deg, #10B981 0%, #0EA5E9 100%); }

        /* 主题系统变量 */
        :root {
            /* 暗夜霓虹主题 */
            --theme-dark-primary: #ff3cac;
            --theme-dark-secondary: #784ba0;
            --theme-dark-bg: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            --theme-dark-text: #fff;
            --theme-dark-card-bg: rgba(45, 0, 54, 0.6);
            --theme-dark-card-border: rgba(255, 60, 172, 0.3);
            --theme-dark-shadow: rgba(255, 60, 172, 0.2);
            
            /* 清新明亮主题 */
            --theme-light-primary: #784ba0;
            --theme-light-secondary: #ff3cac;
            --theme-light-bg: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            --theme-light-text: #2d0036;
            --theme-light-card-bg: rgba(255, 255, 255, 0.6);
            --theme-light-card-border: rgba(120, 75, 160, 0.3);
            --theme-light-shadow: rgba(120, 75, 160, 0.2);
            
            /* 深海蓝调主题 */
            --theme-ocean-primary: #2563eb;
            --theme-ocean-secondary: #8b5cf6;
            --theme-ocean-bg: linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%);
            --theme-ocean-text: #fff;
            --theme-ocean-card-bg: rgba(26, 42, 58, 0.6);
            --theme-ocean-card-border: rgba(37, 99, 235, 0.3);
            --theme-ocean-shadow: rgba(37, 99, 235, 0.2);
            
            /* 森林绿意主题 */
            --theme-forest-primary: #10b981;
            --theme-forest-secondary: #0ea5e9;
            --theme-forest-bg: linear-gradient(135deg, #1a3a2a 0%, #2c503e 100%);
            --theme-forest-text: #fff;
            --theme-forest-card-bg: rgba(26, 58, 42, 0.6);
            --theme-forest-card-border: rgba(16, 185, 129, 0.3);
            --theme-forest-shadow: rgba(16, 185, 129, 0.2);
            
            /* 日落紫霞主题 */
            --theme-sunset-primary: #f43f5e;
            --theme-sunset-secondary: #8b5cf6;
            --theme-sunset-bg: linear-gradient(135deg, #3a1a2a 0%, #502c3e 100%);
            --theme-sunset-text: #fff;
            --theme-sunset-card-bg: rgba(58, 26, 42, 0.6);
            --theme-sunset-card-border: rgba(244, 63, 94, 0.3);
            --theme-sunset-shadow: rgba(244, 63, 94, 0.2);
        }

        /* 主题切换按钮和下拉菜单 */
        .theme-toggle {
            background: none;
            border: none;
            color: var(--theme-dark-primary);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            position: relative;
        }
        .theme-toggle:hover {
            background: rgba(255, 60, 172, 0.1);
            transform: scale(1.1);
        }
        .theme-dropdown {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            background: var(--theme-dark-card-bg);
            border: 1px solid var(--theme-dark-card-border);
            border-radius: 12px;
            padding: 12px;
            box-shadow: 0 4px 24px var(--theme-dark-shadow);
            display: none;
            z-index: 1000;
            min-width: 220px;
            backdrop-filter: blur(10px);
            transform-origin: top left;
            will-change: transform, opacity;
        }
        .theme-dropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 24px;
            width: 16px;
            height: 16px;
            background: var(--theme-dark-card-bg);
            border-left: 1px solid var(--theme-dark-card-border);
            border-top: 1px solid var(--theme-dark-card-border);
            transform: rotate(45deg);
            border-radius: 2px;
        }
        .theme-dropdown.active {
            display: block;
            animation: dropdown-fade 0.2s ease forwards;
        }
        @keyframes dropdown-fade {
            from { 
                opacity: 0; 
                transform: translateY(-10px) scale(0.95);
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1);
            }
        }
        .theme-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: var(--theme-dark-text);
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.2s;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .theme-option::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 60, 172, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.2s;
        }
        .theme-option:hover::before {
            opacity: 1;
        }
        .theme-option.active {
            background: rgba(255, 60, 172, 0.2);
        }
        .theme-option.active::before {
            opacity: 1;
        }
        .theme-preview {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 12px rgba(255, 60, 172, 0.2);
            transition: all 0.2s;
        }
        .theme-option:hover .theme-preview {
            transform: scale(1.1);
            box-shadow: 0 0 16px rgba(255, 60, 172, 0.4);
        }
        .theme-name {
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .theme-option.active .theme-name {
            color: var(--theme-dark-primary);
        }

        /* 主题样式 */
        body.theme-dark {
            background: var(--theme-dark-bg);
            color: var(--theme-dark-text);
        }
        body.theme-light {
            background: var(--theme-light-bg);
            color: var(--theme-light-text);
        }
        body.theme-ocean {
            background: var(--theme-ocean-bg);
            color: var(--theme-ocean-text);
        }
        body.theme-forest {
            background: var(--theme-forest-bg);
            color: var(--theme-forest-text);
        }
        body.theme-sunset {
            background: var(--theme-sunset-bg);
            color: var(--theme-sunset-text);
        }

        /* 主题预览颜色 */
        .theme-preview.dark { background: var(--theme-dark-bg); }
        .theme-preview.light { background: var(--theme-light-bg); }
        .theme-preview.ocean { background: var(--theme-ocean-bg); }
        .theme-preview.forest { background: var(--theme-forest-bg); }
        .theme-preview.sunset { background: var(--theme-sunset-bg); }

        /* 主题相关组件样式 */
        .navbar {
            background: rgba(30, 0, 50, 0.95);
            box-shadow: 0 2px 24px var(--theme-dark-shadow);
            transition: background 0.3s, box-shadow 0.3s;
        }
        .light-theme .navbar {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 24px var(--theme-light-shadow);
        }
        .feature-card {
            background: var(--theme-dark-card-bg);
            border: 1px solid var(--theme-dark-card-border);
            box-shadow: 0 4px 32px var(--theme-dark-shadow);
            transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
        }
        .light-theme .feature-card {
            background: var(--theme-light-card-bg);
            border: 1px solid var(--theme-light-card-border);
            box-shadow: 0 4px 32px var(--theme-light-shadow);
        }
    </style>
</head>
<body>
    <canvas class="particles"></canvas>
    <div class="container">
        <nav class="navbar">
            <div class="navbar-brand">
                <span class="icon-svg">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 21h18M8.5 17v-7.5a2.5 2.5 0 1 1 5 0V17M12 17v-7.5"/><circle cx="12" cy="5" r="2.5"/></svg>
                </span>
                智慧食堂平台
            </div>
            <div class="navbar-nav">
                <a href="#features">核心功能</a>
                <a href="#tutorials">视频教学</a>
                <a href="#contact">联系我们</a>
            </div>
            <div class="navbar-actions">
                <div class="theme-toggle" id="themeToggle" title="切换主题">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                    <div class="theme-dropdown" id="themeDropdown">
                        <div class="theme-option active" data-theme="dark">
                            <div class="theme-preview dark"></div>
                            <div class="theme-name">暗夜霓虹</div>
                        </div>
                        <div class="theme-option" data-theme="light">
                            <div class="theme-preview light"></div>
                            <div class="theme-name">清新明亮</div>
                        </div>
                        <div class="theme-option" data-theme="ocean">
                            <div class="theme-preview ocean"></div>
                            <div class="theme-name">深海蓝调</div>
                        </div>
                        <div class="theme-option" data-theme="forest">
                            <div class="theme-preview forest"></div>
                            <div class="theme-name">森林绿意</div>
                        </div>
                        <div class="theme-option" data-theme="sunset">
                            <div class="theme-preview sunset"></div>
                            <div class="theme-name">日落紫霞</div>
                        </div>
                        <div class="theme-option" data-theme="deep-sea-tech">
                            <div class="theme-preview deep-sea-tech"></div>
                            <div class="theme-name">深海科技</div>
                        </div>
                        <div class="theme-option" data-theme="soft-morandi">
                            <div class="theme-preview soft-morandi"></div>
                            <div class="theme-name">柔光莫兰迪</div>
                        </div>
                        <div class="theme-option" data-theme="minimal-dawn">
                            <div class="theme-preview minimal-dawn"></div>
                            <div class="theme-name">极简晨曦</div>
                        </div>
                        <div class="theme-option" data-theme="dark-neon">
                            <div class="theme-preview dark-neon"></div>
                            <div class="theme-name">暗夜霓虹</div>
                        </div>
                        <div class="theme-option" data-theme="nature-eco">
                            <div class="theme-preview nature-eco"></div>
                            <div class="theme-name">自然生态</div>
                        </div>
                    </div>
                </div>
                <a href="{{ url_for('auth.login') }}" class="btn-neon">登录</a>
                <a href="{{ url_for('auth.register') }}" class="btn-neon" style="background:linear-gradient(90deg,#ff3cac 0%,#784ba0 100%);color:#fff;">免费注册</a>
            </div>
        </nav>
        <section class="hero">
            <div class="hero-title neon">智慧食堂管理平台</div>
            <div class="hero-desc">打造智能化校园食堂管理新生态，让食品安全更透明，让管理更高效，让服务更贴心</div>
            <div class="hero-actions">
                <a href="{{ url_for('auth.register') }}" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"/><path d="M5.5 21a7.5 7.5 0 0 1 13 0"/></svg></span>免费注册</a>
                <a href="#features" class="btn-neon" style="background:linear-gradient(90deg,#784ba0 0%,#ff3cac 100%);color:#fff;">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><circle cx="12" cy="8" r="1"/></svg></span>了解更多</a>
            </div>
            <div class="divider"></div>
        </section>
        <section class="features-section" id="features">
            <div class="features-title">核心功能</div>
            <div class="features-list">
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l2.09 6.26L20 9.27l-5 4.87L16.18 22 12 18.56 7.82 22 9 14.14l-5-4.87 5.91-.91z"/></svg></div>
                    <div class="feature-title">食品安全管理</div>
                    <div class="feature-desc">全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg></div>
                    <div class="feature-title">智能采购系统</div>
                    <div class="feature-desc">智能价格对比，自动生成采购单，简化采购流程，提升采购效率。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18M9 21V9"/></svg></div>
                    <div class="feature-title">出入库管理</div>
                    <div class="feature-desc">完整管理出入库流程，自动生成台账报表，实时监控库存情况。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="16" rx="2"/><path d="M3 10h18"/></svg></div>
                    <div class="feature-title">灵活菜单管理</div>
                    <div class="feature-desc">自定义菜单，营养搭配推荐，满足不同需求。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"/><path d="M5.5 21a7.5 7.5 0 0 1 13 0"/></svg></div>
                    <div class="feature-title">师生用餐管理</div>
                    <div class="feature-desc">刷卡、扫码、预约多种用餐方式，数据实时统计。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"/><path d="M18 21V3"/><path d="M3 9h15"/></svg></div>
                    <div class="feature-title">财务报表分析</div>
                    <div class="feature-desc">自动生成多维度财务报表，辅助决策。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4l3 3"/></svg></div>
                    <div class="feature-title">系统权限管理</div>
                    <div class="feature-desc">多级权限分配，安全高效。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 15a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H7a4 4 0 0 0-4 4z"/><path d="M7 7h10v4H7z"/></svg></div>
                    <div class="feature-title">云端数据备份</div>
                    <div class="feature-desc">数据云端存储，安全可靠，随时恢复。</div>
                </div>
            </div>
        </section>
        
        <!-- 视频教学区域 -->
        <section class="tutorial-section" id="tutorials">
            <div class="tutorial-title">视频教学</div>
            <div class="tutorial-container">
                <div class="tutorial-grid active" id="videoGrid">
                    <!-- 视频卡片将通过JavaScript动态加载 -->
                </div>
                <div class="tutorial-nav">
                    <button class="tutorial-nav-btn" id="prevBtn" disabled>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </button>
                    <div class="tutorial-nav-dots" id="navDots">
                        <!-- 导航点将通过JavaScript动态生成 -->
                    </div>
                    <button class="tutorial-nav-btn" id="nextBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- 视频播放模态框 -->
        <div class="video-modal" id="videoModal">
            <div class="video-container">
                <video class="video-player" controls>
                    <source src="" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <div class="close-modal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </div>
            </div>
        </div>

        <section class="contact-section" id="contact">
            <div class="contact-title">联系我们</div>
            <div class="contact-info">专业的技术团队为您提供7×24小时服务支持<br>电话：18373062333 &nbsp;|&nbsp; 邮箱：<EMAIL> &nbsp;|&nbsp; 湖南·岳阳</div>
            <div class="contact-actions">
                <a href="mailto:<EMAIL>" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M22 6l-10 7L2 6"/></svg></span>邮件咨询</a>
                <a href="tel:18373062333" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92V19a2 2 0 0 1-2.18 2A19.72 19.72 0 0 1 3 5.18 2 2 0 0 1 5 3h2.09a2 2 0 0 1 2 1.72c.13 1.13.37 2.23.72 3.28a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6.29 6.29l1.27-1.27a2 2 0 0 1 2.11-.45c1.05.35 2.15.59 3.28.72A2 2 0 0 1 22 16.92z"/></svg></span>电话咨询</a>
            </div>
        </section>
        <div class="footer">
            <div>© 2025 智慧食堂平台. All rights reserved.</div>
            <div>专注校园食堂智能化管理解决方案</div>
        </div>
    </div>
    <script>
    // 主题管理
    class ThemeManager {
        constructor() {
            this.themeToggle = document.getElementById('themeToggle');
            this.themeDropdown = document.getElementById('themeDropdown');
            this.themeOptions = document.querySelectorAll('.theme-option');
            this.body = document.body;
            this.init();
        }

        init() {
            // 从本地存储加载主题
            const savedTheme = localStorage.getItem('theme') || 'dark';
            this.applyTheme(savedTheme);
            this.updateActiveTheme(savedTheme);

            // 绑定事件
            this.themeToggle.addEventListener('click', this.handleThemeToggle.bind(this));
            document.addEventListener('click', this.handleDocumentClick.bind(this));
            this.themeOptions.forEach(option => {
                option.addEventListener('click', this.handleThemeSelect.bind(this));
            });
        }

        handleThemeToggle(e) {
            e.stopPropagation();
            this.themeDropdown.classList.toggle('active');
        }

        handleDocumentClick(e) {
            if (!this.themeDropdown.contains(e.target) && !this.themeToggle.contains(e.target)) {
                this.themeDropdown.classList.remove('active');
            }
        }

        handleThemeSelect(e) {
            e.stopPropagation();
            const theme = e.currentTarget.dataset.theme;
            this.applyTheme(theme);
            this.updateActiveTheme(theme);
            localStorage.setItem('theme', theme);
            this.themeDropdown.classList.remove('active');
        }

        applyTheme(theme) {
            this.body.className = `theme-${theme}`;
        }

        updateActiveTheme(theme) {
            this.themeOptions.forEach(option => {
                option.classList.toggle('active', option.dataset.theme === theme);
            });
        }
    }

    // 粒子背景管理
    class ParticleBackground {
        constructor() {
            this.canvas = document.querySelector('.particles');
            this.ctx = this.canvas.getContext('2d');
            this.particles = [];
            this.init();
        }

        init() {
            this.resize();
            window.addEventListener('resize', this.resize.bind(this));
            this.createParticles();
            this.animate();
        }

        resize() {
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;
        }

        createParticles() {
            const colors = ['#ff3cac', '#784ba0', '#fff', '#ffb6e6'];
            for (let i = 0; i < 60; i++) {
                this.particles.push({
                    x: Math.random() * this.canvas.width,
                    y: Math.random() * this.canvas.height,
                    r: Math.random() * 2 + 1,
                    dx: (Math.random() - 0.5) * 0.5,
                    dy: (Math.random() - 0.5) * 0.5,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    alpha: Math.random() * 0.5 + 0.5
                });
            }
        }

        animate() {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            
            this.particles.forEach(p => {
                this.ctx.save();
                this.ctx.globalAlpha = p.alpha;
                this.ctx.beginPath();
                this.ctx.arc(p.x, p.y, p.r, 0, 2 * Math.PI);
                this.ctx.fillStyle = p.color;
                this.ctx.shadowColor = p.color;
                this.ctx.shadowBlur = 12;
                this.ctx.fill();
                this.ctx.restore();

                p.x += p.dx;
                p.y += p.dy;

                if (p.x < 0 || p.x > this.canvas.width) p.x = Math.random() * this.canvas.width;
                if (p.y < 0 || p.y > this.canvas.height) p.y = Math.random() * this.canvas.height;
            });

            requestAnimationFrame(this.animate.bind(this));
        }
    }

    // 视频管理
    class VideoManager {
        constructor() {
            this.currentPage = 0;
            this.videosPerPage = 3;
            this.allVideos = [];
            this.videoGrid = document.getElementById('videoGrid');
            this.videoModal = document.getElementById('videoModal');
            this.videoPlayer = this.videoModal.querySelector('video');
            this.closeModal = this.videoModal.querySelector('.close-modal');
            this.prevBtn = document.getElementById('prevBtn');
            this.nextBtn = document.getElementById('nextBtn');
            this.navDots = document.getElementById('navDots');
            this.init();
        }

        init() {
            this.loadVideos();
            this.bindEvents();
        }

        async loadVideos() {
            try {
                const response = await fetch('/api/guide/videos/all');
                const data = await response.json();
                
                if (data.success && data.videos) {
                    this.allVideos = data.videos;
                    this.updateVideoDisplay();
                    this.updateNavigation();
                } else {
                    throw new Error('视频数据格式错误');
                }
            } catch (error) {
                console.error('加载视频失败:', error);
                this.videoGrid.innerHTML = '<div class="error-message">加载视频失败，请稍后重试</div>';
            }
        }

        updateVideoDisplay() {
            this.videoGrid.innerHTML = '';
            const startIndex = this.currentPage * this.videosPerPage;
            const endIndex = Math.min(startIndex + this.videosPerPage, this.allVideos.length);
            const currentVideos = this.allVideos.slice(startIndex, endIndex);

            currentVideos.forEach(video => {
                const videoCard = this.createVideoCard(video);
                this.videoGrid.appendChild(videoCard);
            });

            this.bindVideoEvents();
        }

        createVideoCard(video) {
            const videoCard = document.createElement('div');
            videoCard.className = 'tutorial-card';
            videoCard.dataset.video = video.url;

            videoCard.innerHTML = `
                <div class="tutorial-thumbnail">
                    <img src="${video.thumbnail}" alt="${video.name}" loading="lazy">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </div>
                </div>
                <div class="tutorial-info">
                    <div>
                        <div class="tutorial-name">${video.name}</div>
                        <div class="tutorial-desc">${video.description || '暂无描述'}</div>
                    </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        ${video.duration || '未知时长'}
                    </div>
                </div>
            `;

            return videoCard;
        }

        updateNavigation() {
            const totalPages = Math.ceil(this.allVideos.length / this.videosPerPage);
            
            // 更新导航点
            this.navDots.innerHTML = '';
            for (let i = 0; i < totalPages; i++) {
                const dot = document.createElement('div');
                dot.className = `tutorial-nav-dot ${i === this.currentPage ? 'active' : ''}`;
                dot.addEventListener('click', () => {
                    this.currentPage = i;
                    this.updateVideoDisplay();
                    this.updateNavigation();
                });
                this.navDots.appendChild(dot);
            }

            // 更新按钮状态
            this.prevBtn.disabled = this.currentPage === 0;
            this.nextBtn.disabled = this.currentPage === totalPages - 1;
        }

        bindEvents() {
            // 导航按钮事件
            this.prevBtn.addEventListener('click', () => {
                if (this.currentPage > 0) {
                    this.currentPage--;
                    this.updateVideoDisplay();
                    this.updateNavigation();
                }
            });

            this.nextBtn.addEventListener('click', () => {
                const totalPages = Math.ceil(this.allVideos.length / this.videosPerPage);
                if (this.currentPage < totalPages - 1) {
                    this.currentPage++;
                    this.updateVideoDisplay();
                    this.updateNavigation();
                }
            });

            // 模态框事件
            this.closeModal.addEventListener('click', () => this.closeVideoModal());
            this.videoModal.addEventListener('click', (e) => {
                if (e.target === this.videoModal) {
                    this.closeVideoModal();
                }
            });
        }

        bindVideoEvents() {
            const tutorialCards = document.querySelectorAll('.tutorial-card');
            tutorialCards.forEach(card => {
                card.addEventListener('click', () => {
                    const videoSrc = card.dataset.video;
                    this.playVideo(videoSrc);
                });
            });
        }

        playVideo(videoSrc) {
            this.videoPlayer.src = videoSrc;
            this.videoModal.classList.add('active');
            this.videoPlayer.play().catch(error => {
                console.error('视频播放失败:', error);
                this.closeVideoModal();
            });
        }

        closeVideoModal() {
            this.videoModal.classList.remove('active');
            this.videoPlayer.pause();
            this.videoPlayer.src = '';
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        new ThemeManager();
        new ParticleBackground();
        new VideoManager();
    });
    </script>
</body>
</html>
