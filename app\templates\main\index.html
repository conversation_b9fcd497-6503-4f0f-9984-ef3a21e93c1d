<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 暗夜霓红首页</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            letter-spacing: 0.02em;
            overflow-x: hidden;
            transition: background 0.3s, color 0.3s;
        }
        body.light-theme {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d0036;
        }
        .neon {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            animation: neon-glow 2.5s infinite alternate;
        }
        .light-theme .neon {
            color: #784ba0;
            text-shadow: 0 0 12px #784ba0, 0 0 32px #ff3cac;
            animation: neon-glow-light 2.5s infinite alternate;
        }
        @keyframes neon-glow {
            0% { text-shadow: 0 0 8px #ff3cac, 0 0 16px #784ba0; }
            100% { text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0; }
        }
        @keyframes neon-glow-light {
            0% { text-shadow: 0 0 8px #784ba0, 0 0 16px #ff3cac; }
            100% { text-shadow: 0 0 24px #784ba0, 0 0 48px #ff3cac; }
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .navbar {
            background: rgba(30,0,50,0.95);
            box-shadow: 0 2px 24px #ff3cac55;
            padding: 18px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 0 0 18px 18px;
            margin-bottom: 18px;
            transition: background 0.3s, box-shadow 0.3s;
        }
        .light-theme .navbar {
            background: rgba(255,255,255,0.95);
            box-shadow: 0 2px 24px #784ba055;
        }
        .navbar-brand {
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            letter-spacing: 2px;
            text-shadow: 0 0 12px #ff3cac, 0 0 32px #784ba0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .icon-svg {
            display: inline-flex;
            vertical-align: middle;
            margin-right: 8px;
            filter: drop-shadow(0 0 6px #ff3cac88);
        }
        .navbar-nav {
            display: flex;
            gap: 32px;
        }
        .navbar-nav a {
            color: #fff;
            font-size: 1.1rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s, text-shadow 0.2s;
            text-shadow: 0 0 4px #784ba0;
        }
        .navbar-nav a:hover {
            color: #ff3cac;
            text-shadow: 0 0 12px #ff3cac;
        }
        .navbar-actions {
            display: flex;
            gap: 16px;
        }
        .btn-neon {
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            color: #fff;
            border: none;
            border-radius: 12px;
            padding: 12px 32px;
            font-size: 1.1rem;
            font-weight: 700;
            box-shadow: 0 0 18px #ff3cac99, 0 0 4px #fff2;
            transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
            position: relative;
            overflow: hidden;
        }
        .btn-neon:after {
            content: '';
            position: absolute;
            left: -50%;
            top: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, #ff3cac55 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .btn-neon:hover {
            background: linear-gradient(90deg, #784ba0 0%, #ff3cac 100%);
            box-shadow: 0 0 32px #ff3caccc, 0 0 8px #fff4;
            transform: scale(1.04);
        }
        .btn-neon:hover:after {
            opacity: 0.3;
        }
        .hero {
            text-align: center;
            padding: 120px 0 60px 0;
            position: relative;
        }
        .hero::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            transform: translateX(-50%);
            width: 80vw;
            height: 100%;
            background: radial-gradient(circle at 50% 30%, #ff3cac33 0%, transparent 80%);
            z-index: 0;
        }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 18px;
            color: #fff;
            text-shadow: 0 0 24px #ff3cac, 0 0 48px #784ba0;
            position: relative;
            z-index: 1;
        }
        .hero-desc {
            font-size: 1.3rem;
            color: #ffb6e6;
            margin-bottom: 36px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            position: relative;
            z-index: 1;
        }
        .hero-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
            position: relative;
            z-index: 1;
        }
        .divider {
            width: 100px;
            height: 4px;
            margin: 32px auto 0 auto;
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            border-radius: 2px;
            box-shadow: 0 0 12px #ff3cac99;
        }
        .features-section {
            margin: 60px 0 40px 0;
        }
        .features-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }
        .feature-card {
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22, 0 0 0 2px #ff3cac33 inset;
            padding: 40px 28px 32px 28px;
            text-align: center;
            color: #fff;
            transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
            position: relative;
            overflow: hidden;
        }
        .light-theme .feature-card {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
            box-shadow: 0 4px 32px #784ba022, 0 0 0 2px #784ba033 inset;
            color: #2d0036;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.04);
            box-shadow: 0 8px 48px #ff3cac77, 0 0 0 2px #ff3cac99 inset;
        }
        .light-theme .feature-card:hover {
            box-shadow: 0 8px 48px #784ba077, 0 0 0 2px #784ba099 inset;
        }
        .feature-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 18px;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 1px;
        }
        .feature-desc {
            color: #ffb6e6;
            font-size: 1rem;
        }
        .light-theme .feature-desc {
            color: #784ba0;
        }
        .contact-section {
            margin: 60px 0 0 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            padding: 48px 24px;
            color: #fff;
            position: relative;
            transition: background 0.3s;
        }
        .light-theme .contact-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .contact-title {
            font-size: 2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-align: center;
            text-shadow: 0 0 12px #ff3cac;
        }
        .contact-info {
            text-align: center;
            color: #ffb6e6;
            font-size: 1.1rem;
            margin-bottom: 18px;
        }
        .light-theme .contact-info {
            color: #784ba0;
        }
        .contact-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
        }
        .footer {
            text-align: center;
            color: #ffb6e6;
            padding: 32px 0 12px 0;
            font-size: 1rem;
            margin-top: 60px;
            letter-spacing: 1px;
        }
        .light-theme .footer {
            color: #784ba0;
        }
        /* 粒子背景装饰 */
        .particles {
            position: fixed;
            left: 0; top: 0; width: 100vw; height: 100vh;
            pointer-events: none;
            z-index: 0;
        }
        /* 视频教学区域样式 */
        .tutorial-section {
            margin: 60px 0;
            padding: 40px 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 20px;
            box-shadow: 0 4px 32px #ff3cac22;
            transition: background 0.3s;
        }
        .light-theme .tutorial-section {
            background: linear-gradient(135deg, #ffffff 60%, #784ba022 100%);
        }
        .tutorial-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 32px;
            text-shadow: 0 0 12px #ff3cac;
        }
        .tutorial-container {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        .tutorial-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        .tutorial-grid.active {
            opacity: 1;
            transform: translateX(0);
        }
        .tutorial-card {
            background: rgba(45, 0, 54, 0.6);
            border-radius: 16px;
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
            cursor: pointer;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .light-theme .tutorial-card {
            background: rgba(255, 255, 255, 0.6);
        }
        .tutorial-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 32px #ff3cac55;
        }
        .tutorial-thumbnail {
            width: 100%;
            height: 200px;
            background: #1a0025;
            position: relative;
            overflow: hidden;
        }
        .tutorial-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        .tutorial-card:hover .tutorial-thumbnail img {
            transform: scale(1.1);
        }
        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 48px;
            height: 48px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.3s, background-color 0.3s;
        }
        .tutorial-card:hover .play-icon {
            transform: translate(-50%, -50%) scale(1.1);
            background: rgba(255, 60, 172, 1);
        }
        .tutorial-info {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .tutorial-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #fff;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        .tutorial-desc {
            font-size: 0.95rem;
            color: #ffb6e6;
            line-height: 1.5;
            flex-grow: 1;
        }
        .light-theme .tutorial-desc {
            color: #784ba0;
        }
        .tutorial-duration {
            font-size: 0.85rem;
            color: #ff3cac;
            margin-top: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .tutorial-duration svg {
            width: 16px;
            height: 16px;
        }
        .tutorial-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            margin-top: 32px;
        }
        .tutorial-nav-btn {
            background: rgba(255, 60, 172, 0.2);
            border: 2px solid #ff3cac;
            color: #ff3cac;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-btn:hover {
            background: rgba(255, 60, 172, 0.4);
            transform: scale(1.1);
        }
        .tutorial-nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .tutorial-nav-dots {
            display: flex;
            gap: 8px;
        }
        .tutorial-nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 60, 172, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        .tutorial-nav-dot.active {
            background: #ff3cac;
            transform: scale(1.2);
        }
        @media (max-width: 1200px) {
            .tutorial-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 768px) {
            .tutorial-grid {
                grid-template-columns: 1fr;
            }
            .tutorial-thumbnail {
                height: 180px;
            }
        }
        /* 视频模态框样式 */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .video-modal.active {
            display: flex;
            opacity: 1;
        }
        .video-container {
            position: relative;
            width: 90%;
            max-width: 1000px;
            margin: auto;
            background: #1a0025;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 0 48px #ff3cac55;
        }
        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
        }
        .close-modal {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 60, 172, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 0 24px #ff3cac;
            transition: transform 0.2s;
        }
        .close-modal:hover {
            transform: scale(1.1);
        }
        .theme-toggle {
            background: none;
            border: none;
            color: #ff3cac;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            position: relative;
        }
        .theme-toggle:hover {
            background: rgba(255, 60, 172, 0.1);
            transform: scale(1.1);
        }
        .theme-dropdown {
            position: absolute;
            top: 100%;
            right: -20px;
            background: rgba(45, 0, 54, 0.95);
            border-radius: 12px;
            padding: 12px;
            margin-top: 12px;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
            display: none;
            z-index: 1000;
            min-width: 220px;
            backdrop-filter: blur(10px);
            transform-origin: top right;
        }
        .theme-dropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 24px;
            width: 16px;
            height: 16px;
            background: rgba(45, 0, 54, 0.95);
            transform: rotate(45deg);
            border-radius: 2px;
        }
        .theme-dropdown.active {
            display: block;
            animation: dropdown-fade 0.3s ease;
        }
        @keyframes dropdown-fade {
            from { 
                opacity: 0; 
                transform: translateY(-10px) scale(0.95);
            }
            to { 
                opacity: 1; 
                transform: translateY(0) scale(1);
            }
        }
        .theme-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #fff;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .theme-option::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 60, 172, 0.1) 0%, transparent 100%);
            opacity: 0;
            transition: opacity 0.3s;
        }
        .theme-option:hover::before {
            opacity: 1;
        }
        .theme-option.active {
            background: rgba(255, 60, 172, 0.2);
        }
        .theme-option.active::before {
            opacity: 1;
        }
        .theme-preview {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            margin-right: 16px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 12px rgba(255, 60, 172, 0.2);
            transition: all 0.3s;
        }
        .theme-option:hover .theme-preview {
            transform: scale(1.1);
            box-shadow: 0 0 16px rgba(255, 60, 172, 0.4);
        }
        .theme-name {
            font-size: 1rem;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        .theme-option.active .theme-name {
            color: #ff3cac;
        }
        /* 主题样式 */
        body.theme-dark {
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
        }
        body.theme-light {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2d0036;
        }
        body.theme-ocean {
            background: linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%);
            color: #fff;
        }
        body.theme-forest {
            background: linear-gradient(135deg, #1a3a2a 0%, #2c503e 100%);
            color: #fff;
        }
        body.theme-sunset {
            background: linear-gradient(135deg, #3a1a2a 0%, #502c3e 100%);
            color: #fff;
        }
        /* 主题预览颜色 */
        .theme-preview.dark { background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%); }
        .theme-preview.light { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); }
        .theme-preview.ocean { background: linear-gradient(135deg, #1a2a3a 0%, #2c3e50 100%); }
        .theme-preview.forest { background: linear-gradient(135deg, #1a3a2a 0%, #2c503e 100%); }
        .theme-preview.sunset { background: linear-gradient(135deg, #3a1a2a 0%, #502c3e 100%); }
    </style>
</head>
<body>
    <canvas class="particles"></canvas>
    <div class="container">
        <nav class="navbar">
            <div class="navbar-brand">
                <span class="icon-svg">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 21h18M8.5 17v-7.5a2.5 2.5 0 1 1 5 0V17M12 17v-7.5"/><circle cx="12" cy="5" r="2.5"/></svg>
                </span>
                智慧食堂平台
            </div>
            <div class="navbar-nav">
                <a href="#features">核心功能</a>
                <a href="#tutorials">视频教学</a>
                <a href="#contact">联系我们</a>
            </div>
            <div class="navbar-actions">
                <div class="theme-toggle" id="themeToggle" title="切换主题">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                    <div class="theme-dropdown" id="themeDropdown">
                        <div class="theme-option active" data-theme="dark">
                            <div class="theme-preview dark"></div>
                            <div class="theme-name">暗夜霓虹</div>
                        </div>
                        <div class="theme-option" data-theme="light">
                            <div class="theme-preview light"></div>
                            <div class="theme-name">清新明亮</div>
                        </div>
                        <div class="theme-option" data-theme="ocean">
                            <div class="theme-preview ocean"></div>
                            <div class="theme-name">深海蓝调</div>
                        </div>
                        <div class="theme-option" data-theme="forest">
                            <div class="theme-preview forest"></div>
                            <div class="theme-name">森林绿意</div>
                        </div>
                        <div class="theme-option" data-theme="sunset">
                            <div class="theme-preview sunset"></div>
                            <div class="theme-name">日落紫霞</div>
                        </div>
                    </div>
                </div>
                <a href="{{ url_for('auth.login') }}" class="btn-neon">登录</a>
                <a href="{{ url_for('auth.register') }}" class="btn-neon" style="background:linear-gradient(90deg,#ff3cac 0%,#784ba0 100%);color:#fff;">免费注册</a>
            </div>
        </nav>
        <section class="hero">
            <div class="hero-title neon">智慧食堂管理平台</div>
            <div class="hero-desc">打造智能化校园食堂管理新生态，让食品安全更透明，让管理更高效，让服务更贴心</div>
            <div class="hero-actions">
                <a href="{{ url_for('auth.register') }}" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"/><path d="M5.5 21a7.5 7.5 0 0 1 13 0"/></svg></span>免费注册</a>
                <a href="#features" class="btn-neon" style="background:linear-gradient(90deg,#784ba0 0%,#ff3cac 100%);color:#fff;">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><circle cx="12" cy="8" r="1"/></svg></span>了解更多</a>
            </div>
            <div class="divider"></div>
        </section>
        <section class="features-section" id="features">
            <div class="features-title">核心功能</div>
            <div class="features-list">
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2l2.09 6.26L20 9.27l-5 4.87L16.18 22 12 18.56 7.82 22 9 14.14l-5-4.87 5.91-.91z"/></svg></div>
                    <div class="feature-title">食品安全管理</div>
                    <div class="feature-desc">全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"/><circle cx="20" cy="21" r="1"/><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/></svg></div>
                    <div class="feature-title">智能采购系统</div>
                    <div class="feature-desc">智能价格对比，自动生成采购单，简化采购流程，提升采购效率。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"/><path d="M3 9h18M9 21V9"/></svg></div>
                    <div class="feature-title">出入库管理</div>
                    <div class="feature-desc">完整管理出入库流程，自动生成台账报表，实时监控库存情况。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="16" rx="2"/><path d="M3 10h18"/></svg></div>
                    <div class="feature-title">灵活菜单管理</div>
                    <div class="feature-desc">自定义菜单，营养搭配推荐，满足不同需求。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="7" r="4"/><path d="M5.5 21a7.5 7.5 0 0 1 13 0"/></svg></div>
                    <div class="feature-title">师生用餐管理</div>
                    <div class="feature-desc">刷卡、扫码、预约多种用餐方式，数据实时统计。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"/><path d="M18 21V3"/><path d="M3 9h15"/></svg></div>
                    <div class="feature-title">财务报表分析</div>
                    <div class="feature-desc">自动生成多维度财务报表，辅助决策。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4l3 3"/></svg></div>
                    <div class="feature-title">系统权限管理</div>
                    <div class="feature-desc">多级权限分配，安全高效。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 15a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H7a4 4 0 0 0-4 4z"/><path d="M7 7h10v4H7z"/></svg></div>
                    <div class="feature-title">云端数据备份</div>
                    <div class="feature-desc">数据云端存储，安全可靠，随时恢复。</div>
                </div>
            </div>
        </section>
        
        <!-- 视频教学区域 -->
        <section class="tutorial-section" id="tutorials">
            <div class="tutorial-title">视频教学</div>
            <div class="tutorial-container">
                <div class="tutorial-grid active" id="videoGrid">
                    <!-- 视频卡片将通过JavaScript动态加载 -->
                </div>
                <div class="tutorial-nav">
                    <button class="tutorial-nav-btn" id="prevBtn" disabled>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </button>
                    <div class="tutorial-nav-dots" id="navDots">
                        <!-- 导航点将通过JavaScript动态生成 -->
                    </div>
                    <button class="tutorial-nav-btn" id="nextBtn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </button>
                </div>
            </div>
        </section>

        <!-- 视频播放模态框 -->
        <div class="video-modal" id="videoModal">
            <div class="video-container">
                <video class="video-player" controls>
                    <source src="" type="video/mp4">
                    您的浏览器不支持视频播放
                </video>
                <div class="close-modal">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </div>
            </div>
        </div>

        <section class="contact-section" id="contact">
            <div class="contact-title">联系我们</div>
            <div class="contact-info">专业的技术团队为您提供7×24小时服务支持<br>电话：18373062333 &nbsp;|&nbsp; 邮箱：<EMAIL> &nbsp;|&nbsp; 湖南·岳阳</div>
            <div class="contact-actions">
                <a href="mailto:<EMAIL>" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="4" width="20" height="16" rx="2"/><path d="M22 6l-10 7L2 6"/></svg></span>邮件咨询</a>
                <a href="tel:18373062333" class="btn-neon">
                  <span class="icon-svg"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92V19a2 2 0 0 1-2.18 2A19.72 19.72 0 0 1 3 5.18 2 2 0 0 1 5 3h2.09a2 2 0 0 1 2 1.72c.13 1.13.37 2.23.72 3.28a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6.29 6.29l1.27-1.27a2 2 0 0 1 2.11-.45c1.05.35 2.15.59 3.28.72A2 2 0 0 1 22 16.92z"/></svg></span>电话咨询</a>
            </div>
        </section>
        <div class="footer">
            <div>© 2025 智慧食堂平台. All rights reserved.</div>
            <div>专注校园食堂智能化管理解决方案</div>
        </div>
    </div>
    <script>
    // 主题切换功能
    const themeToggle = document.getElementById('themeToggle');
    const themeDropdown = document.getElementById('themeDropdown');
    const body = document.body;
    const themeOptions = document.querySelectorAll('.theme-option');
    
    // 检查本地存储中的主题设置
    const savedTheme = localStorage.getItem('theme') || 'dark';
    body.className = `theme-${savedTheme}`;
    updateActiveTheme(savedTheme);

    // 主题切换事件
    themeToggle.addEventListener('click', (e) => {
        e.stopPropagation();
        themeDropdown.classList.toggle('active');
    });

    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', (e) => {
        if (!themeToggle.contains(e.target)) {
            themeDropdown.classList.remove('active');
        }
    });

    // 主题选项点击事件
    themeOptions.forEach(option => {
        option.addEventListener('click', () => {
            const theme = option.dataset.theme;
            body.className = `theme-${theme}`;
            updateActiveTheme(theme);
            localStorage.setItem('theme', theme);
            themeDropdown.classList.remove('active');
        });
    });

    // 更新活动主题样式
    function updateActiveTheme(theme) {
        themeOptions.forEach(option => {
            option.classList.toggle('active', option.dataset.theme === theme);
        });
    }

    // 粒子背景动画
    const canvas = document.querySelector('.particles');
    const ctx = canvas.getContext('2d');
    let w = window.innerWidth, h = window.innerHeight;
    let particles = [];
    function resize() {
        w = window.innerWidth; h = window.innerHeight;
        canvas.width = w; canvas.height = h;
    }
    window.addEventListener('resize', resize);
    resize();
    function randomColor() {
        const colors = ['#ff3cac', '#784ba0', '#fff', '#ffb6e6'];
        return colors[Math.floor(Math.random()*colors.length)];
    }
    function createParticle() {
        return {
            x: Math.random()*w,
            y: Math.random()*h,
            r: Math.random()*2+1,
            dx: (Math.random()-0.5)*0.5,
            dy: (Math.random()-0.5)*0.5,
            color: randomColor(),
            alpha: Math.random()*0.5+0.5
        };
    }
    for(let i=0;i<60;i++) particles.push(createParticle());
    function draw() {
        ctx.clearRect(0,0,w,h);
        for(const p of particles) {
            ctx.save();
            ctx.globalAlpha = p.alpha;
            ctx.beginPath();
            ctx.arc(p.x,p.y,p.r,0,2*Math.PI);
            ctx.fillStyle = p.color;
            ctx.shadowColor = p.color;
            ctx.shadowBlur = 12;
            ctx.fill();
            ctx.restore();
            p.x += p.dx; p.y += p.dy;
            if(p.x<0||p.x>w) p.x = Math.random()*w;
            if(p.y<0||p.y>h) p.y = Math.random()*h;
        }
        requestAnimationFrame(draw);
    }
    draw();

    // 加载视频数据
    let currentPage = 0;
    let videosPerPage = 3;
    let allVideos = [];

    function loadVideos() {
        fetch('/api/guide/videos/all')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.videos) {
                    allVideos = data.videos;
                    updateVideoDisplay();
                    updateNavigation();
                }
            })
            .catch(error => {
                console.error('加载视频失败:', error);
                const videoGrid = document.getElementById('videoGrid');
                videoGrid.innerHTML = '<div class="error-message">加载视频失败，请稍后重试</div>';
            });
    }

    function updateVideoDisplay() {
        const videoGrid = document.getElementById('videoGrid');
        videoGrid.innerHTML = ''; // 清空现有内容

        const startIndex = currentPage * videosPerPage;
        const endIndex = Math.min(startIndex + videosPerPage, allVideos.length);
        const currentVideos = allVideos.slice(startIndex, endIndex);

        currentVideos.forEach(video => {
            const videoCard = document.createElement('div');
            videoCard.className = 'tutorial-card';
            videoCard.dataset.video = video.url;

            videoCard.innerHTML = `
                <div class="tutorial-thumbnail">
                    <img src="${video.thumbnail}" alt="${video.name}">
                    <div class="play-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#fff" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                    </div>
                </div>
                <div class="tutorial-info">
                    <div>
                        <div class="tutorial-name">${video.name}</div>
                        <div class="tutorial-desc">${video.description || '暂无描述'}</div>
                    </div>
                    <div class="tutorial-duration">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        ${video.duration || '未知时长'}
                    </div>
                </div>
            `;

            videoGrid.appendChild(videoCard);
        });

        // 重新绑定视频点击事件
        bindVideoEvents();
    }

    function updateNavigation() {
        const totalPages = Math.ceil(allVideos.length / videosPerPage);
        const navDots = document.getElementById('navDots');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        // 更新导航点
        navDots.innerHTML = '';
        for (let i = 0; i < totalPages; i++) {
            const dot = document.createElement('div');
            dot.className = `tutorial-nav-dot ${i === currentPage ? 'active' : ''}`;
            dot.addEventListener('click', () => {
                currentPage = i;
                updateVideoDisplay();
                updateNavigation();
            });
            navDots.appendChild(dot);
        }

        // 更新按钮状态
        prevBtn.disabled = currentPage === 0;
        nextBtn.disabled = currentPage === totalPages - 1;
    }

    // 绑定导航按钮事件
    document.getElementById('prevBtn').addEventListener('click', () => {
        if (currentPage > 0) {
            currentPage--;
            updateVideoDisplay();
            updateNavigation();
        }
    });

    document.getElementById('nextBtn').addEventListener('click', () => {
        const totalPages = Math.ceil(allVideos.length / videosPerPage);
        if (currentPage < totalPages - 1) {
            currentPage++;
            updateVideoDisplay();
            updateNavigation();
        }
    });

    // 绑定视频点击事件
    function bindVideoEvents() {
        const tutorialCards = document.querySelectorAll('.tutorial-card');
        const videoModal = document.getElementById('videoModal');
        const videoPlayer = videoModal.querySelector('video');
        const closeModal = videoModal.querySelector('.close-modal');

        tutorialCards.forEach(card => {
            card.addEventListener('click', () => {
                const videoSrc = card.dataset.video;
                videoPlayer.src = videoSrc;
                videoModal.classList.add('active');
                videoPlayer.play();
            });
        });

        closeModal.addEventListener('click', () => {
            videoModal.classList.remove('active');
            videoPlayer.pause();
            videoPlayer.src = '';
        });

        videoModal.addEventListener('click', (e) => {
            if (e.target === videoModal) {
                videoModal.classList.remove('active');
                videoPlayer.pause();
                videoPlayer.src = '';
            }
        });
    }

    // 页面加载完成后加载视频
    document.addEventListener('DOMContentLoaded', loadVideos);
    </script>
</body>
</html>
