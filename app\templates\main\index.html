<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 专业的校园食堂管理系统</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">

    <!-- 引入系统CSS框架 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/font-awesome.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/table-optimization.css') }}">

    <!-- CSP Nonce -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'nonce-{{ csp_nonce }}'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';">
</head>
<body class="bg-light">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top shadow">
        <div class="container">
            <!-- Logo -->
            <a class="navbar-brand fw-bold fs-4" href="#">
                <span class="icon-svg">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#ff3cac" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="
                </span>
                智慧食堂平台
            </a>

            <!-- 移动端切换按钮 -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- 导航菜单 -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">核心功能</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">联系我们</a>
                    </li>
                </ul>

                <!-- 登录注册按钮 -->
                <div class="d-flex gap-2">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-outline-light">登录</a>
                    <a href="{{ url_for('auth.register') }}" class="btn btn-light text-primary fw-bold">免费注册</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主横幅 -->
    <section class="bg-primary text-white" style="padding-top: 120px; padding-bottom: 80px;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">智慧食堂管理平台</h1>
                    <p class="lead mb-4">
                        专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯，
                        为校园食堂管理提供高效便捷的技术支持。
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg text-primary fw-bold">
                            <i class="fa fa-user-plus me-2"></i>免费注册
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg">
                            <i class="fa fa-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="p-4">
                        <i class="fa fa-cutlery display-1 text-white-50"></i>
                        <div class="mt-3">
                            <span class="badge bg-light text-primary fs-6 px-3 py-2">
                                <i class="fa fa-gift me-1"></i>永久免费使用
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频演示区域 -->
    <section class="py-5 bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 text-white">
                    <h2 class="fw-bold mb-4">3分钟了解系统全貌</h2>
                    <p class="lead mb-4">观看产品演示视频，快速了解食堂管理系统的强大功能</p>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fa fa-check-circle me-2"></i>完整功能演示</li>
                        <li class="mb-2"><i class="fa fa-check-circle me-2"></i>操作流程指导</li>
                        <li class="mb-2"><i class="fa fa-check-circle me-2"></i>实际案例展示</li>
                    </ul>
                    <button class="btn btn-light btn-lg mt-3" id="mainVideoBtn">
                        <i class="fa fa-play-circle me-2"></i>观看完整演示
                    </button>
                </div>
                <div class="col-lg-6">
                    <div class="video-thumbnail-container position-relative">
                        <div class="video-placeholder bg-white rounded shadow-lg p-5 text-center" style="cursor: pointer;" id="videoThumbnail">
                            <i class="fa fa-video-camera text-primary" style="font-size: 4rem;"></i>
                            <h5 class="mt-3 text-dark">系统演示视频</h5>
                            <p class="text-muted">点击播放完整功能演示</p>
                        </div>
                        <div class="play-overlay position-absolute top-50 start-50 translate-middle">
                            <div class="play-button bg-primary rounded-circle d-flex align-items-center justify-content-center shadow" style="width: 80px; height: 80px;">
                                <i class="fa fa-play text-white fs-1"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心功能 -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">核心功能</h2>
                <p class="lead text-muted">八大智能化功能，全面保障食堂安全</p>
                <div class="mt-3">
                    <span class="badge bg-primary me-2">点击功能卡片观看详细演示</span>
                </div>
            </div>

            <div class="row g-4">
                <!-- 功能卡片1 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="food_safety">
                        <div class="card-body text-center p-4">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-shield text-primary fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">食品安全管理</h5>
                            <p class="card-text text-muted">
                                全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-primary btn-sm video-preview-btn" data-module="food_safety">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片2 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="purchase_management">
                        <div class="card-body text-center p-4">
                            <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-shopping-cart text-success fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">智能采购系统</h5>
                            <p class="card-text text-muted">
                                智能价格对比，自动生成采购单，简化采购流程，提升采购效率。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-success btn-sm video-preview-btn" data-module="purchase_management">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片3 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="inventory_management">
                        <div class="card-body text-center p-4">
                            <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-exchange text-info fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">出入库管理</h5>
                            <p class="card-text text-muted">
                                完整管理出入库流程，自动生成台账报表，实时监控库存情况。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-info btn-sm video-preview-btn" data-module="inventory_management">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片4 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="menu_management">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-list text-warning fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">灵活菜单管理</h5>
                            <p class="card-text text-muted">
                                支持周菜单灵活安排，一键生成带价格及营养分析的带量食谱。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-warning btn-sm video-preview-btn" data-module="menu_management">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片5 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="inspection_system">
                        <div class="card-body text-center p-4">
                            <div class="bg-danger bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-search text-danger fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">智能检查系统</h5>
                            <p class="card-text text-muted">
                                员工扫码上传食堂状况，管理员在线评价反馈，实时监控运营状态。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-danger btn-sm video-preview-btn" data-module="inspection_system">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片6 -->
                <div class="col-md-6 col-lg-4">
                    <div class="card h-100 shadow-sm border-0 feature-card" data-module="family_dining">
                        <div class="card-body text-center p-4">
                            <div class="bg-secondary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="fa fa-users text-secondary fs-2"></i>
                            </div>
                            <h5 class="card-title fw-bold">家校共陪餐</h5>
                            <p class="card-text text-muted">
                                邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通。
                            </p>
                            <div class="mt-3">
                                <button class="btn btn-outline-secondary btn-sm video-preview-btn" data-module="family_dining">
                                    <i class="fa fa-play-circle me-1"></i>观看演示
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频教学与帮助区块 -->
    <section id="tutorials" class="py-5 bg-white">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">视频教学与帮助</h2>
                <p class="text-muted">系统化教学视频与常见问题，助您轻松上手</p>
            </div>
            <div class="row g-4" id="tutorialVideoList">
                <!-- 教学视频卡片示例，可根据后端数据动态渲染 -->
                <div class="col-md-4">
                    <div class="card h-100 shadow-sm tutorial-card" data-video-module="food_safety">
                        <img src="{{ url_for('static', filename='images/tutorials/food_safety.jpg') }}" class="card-img-top" alt="食品安全管理教学视频">
                        <div class="card-body">
                            <h5 class="card-title">食品安全管理教学</h5>
                            <p class="card-text">全流程食品安全操作演示，规范每一步。</p>
                            <button class="btn btn-primary play-tutorial-video-btn">
                                <i class="fa fa-play-circle me-2"></i>观看教学
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                     <div class="card h-100 shadow-sm tutorial-card" data-video-module="purchase_management">
                        <img src="{{ url_for('static', filename='images/tutorials/purchase_management.jpg') }}" class="card-img-top" alt="智能采购系统教学视频">
                        <div class="card-body">
                            <h5 class="card-title">智能采购系统教学</h5>
                            <p class="card-text">采购流程全解读，提升效率。</p>
                            <button class="btn btn-primary play-tutorial-video-btn">
                                <i class="fa fa-play-circle me-2"></i>观看教学
                            </button>
                        </div>
                    </div>
                </div>
                 <div class="col-md-4">
                    <div class="card h-100 shadow-sm tutorial-card" data-video-module="inventory_management">
                        <img src="{{ url_for('static', filename='images/tutorials/inventory_management.jpg') }}" class="card-img-top" alt="出入库管理教学视频">
                        <div class="card-body">
                            <h5 class="card-title">出入库管理教学</h5>
                            <p class="card-text">库存管理规范操作，数据实时掌控。</p>
                            <button class="btn btn-primary play-tutorial-video-btn">
                                <i class="fa fa-play-circle me-2"></i>观看教学
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-4">
                <a href="/help" class="btn btn-outline-primary me-2"><i class="fa fa-book me-1"></i>查看操作指南</a>
                <a href="/help#faq" class="btn btn-outline-secondary"><i class="fa fa-question-circle me-1"></i>常见问题FAQ</a>
            </div>
        </div>
    </section>

    <!-- 用户评价区块 -->
    <section id="testimonials" class="py-5 bg-light">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">用户评价</h2>
                <p class="text-muted">真实用户的声音，见证平台价值</p>
            </div>
            <div class="row g-4 justify-content-center">
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <p class="card-text">“平台让我们学校食堂管理变得高效又透明，家长和老师都很满意！”</p>
                            <div class="d-flex align-items-center mt-3">
                                <img src="{{ url_for('static', filename='images/avatar1.png') }}" alt="用户头像" class="rounded-circle me-3" width="48" height="48">
                                <div>
                                    <div class="fw-bold">李老师</div>
                                    <small class="text-muted">岳阳市某小学</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <p class="card-text">“操作简单，功能齐全，售后响应快，值得推荐！”</p>
                            <div class="d-flex align-items-center mt-3">
                                <img src="{{ url_for('static', filename='images/avatar2.png') }}" alt="用户头像" class="rounded-circle me-3" width="48" height="48">
                                <div>
                                    <div class="fw-bold">王校长</div>
                                    <small class="text-muted">长沙市某中学</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="bg-light py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold text-dark mb-3">联系我们</h2>
                <p class="lead text-muted">专业的技术团队为您提供7×24小时服务支持</p>
            </div>

            <div class="row g-4">
                <!-- 联系信息 -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <h5 class="card-title fw-bold mb-4">
                                <i class="fa fa-phone text-primary me-2"></i>联系方式
                            </h5>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-phone text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">电话咨询</small>
                                        <div class="fw-bold">18373062333</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-envelope text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">邮件咨询</small>
                                        <div class="fw-bold"><EMAIL></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-map-marker text-primary me-3"></i>
                                    <div>
                                        <small class="text-muted">服务地区</small>
                                        <div class="fw-bold">湖南.岳阳</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 免费使用标签 -->
                            <div class="alert alert-success border-0 mt-4">
                                <div class="d-flex align-items-center">
                                    <i class="fa fa-gift me-2"></i>
                                    <div>
                                        <div class="fw-bold">免费使用！永不停机</div>
                                        <small>专业技术支持 · 7×24小时服务</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 在线咨询表单 -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body p-4">
                            <h5 class="card-title fw-bold mb-4">
                                <i class="fa fa-comments text-primary me-2"></i>在线咨询
                            </h5>

                            <form id="consultationForm" method="POST" action="{{ url_for('consultation.submit_consultation') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">姓名</label>
                                        <input type="text" class="form-control" name="name" placeholder="请输入您的姓名" required minlength="2" maxlength="50">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">联系方式类型</label>
                                        <select class="form-select" name="contact_type" required>
                                            <option value="微信">微信</option>
                                            <option value="电话">电话</option>
                                            <option value="邮箱">邮箱</option>
                                        </select>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">联系方式</label>
                                        <input type="text" class="form-control" name="contact_value" placeholder="请输入您的联系方式" required minlength="3" maxlength="100">
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label">咨询内容</label>
                                        <textarea class="form-control" name="content" rows="4" placeholder="请详细描述您的需求，包括学校规模、具体功能需求等" required minlength="10" maxlength="1000"></textarea>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fa fa-paper-plane me-2"></i>提交咨询
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <i class="fa fa-cutlery me-2"></i>
                        <span class="fw-bold">智慧食堂平台</span>
                    </div>
                    <small class="text-muted">专注校园食堂智能化管理解决方案</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">© 2025 智慧食堂平台. All rights reserved.</small>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary position-fixed" style="bottom: 40px; right: 40px; display: none; z-index: 9999; border-radius: 50%; width: 48px; height: 48px;">
        <i class="fa fa-arrow-up"></i>
    </button>
    <script nonce="{{ csp_nonce }}">
        window.addEventListener('scroll', function() {
            document.getElementById('backToTop').style.display = window.scrollY > 300 ? 'block' : 'none';
        });
        document.getElementById('backToTop').onclick = function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };
    </script>

    <!-- 视频播放模态框 -->
    <div class="modal fade" id="videoModal" tabindex="-1" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel">功能演示视频</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="ratio ratio-16x9">
                        <iframe id="videoFrame" src="" frameborder="0" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>

    <!-- 视频功能脚本 -->
    <script nonce="{{ csp_nonce }}">
        document.addEventListener('DOMContentLoaded', function() {
            const videoModal = new bootstrap.Modal(document.getElementById('videoModal'));
            const videoFrame = document.getElementById('videoFrame');

            // 视频数据映射 (示例数据，实际应从后端获取更全面的教学视频列表)
            const videoData = {
                'main': {
                    title: '系统完整演示',
                    url: '/api/videos/main-demo'
                },
                // 现有功能演示视频
                'food_safety': {
                    title: '食品安全管理演示',
                    url: '/api/videos/food-safety'
                },
                'purchase_management': {
                    title: '智能采购系统演示',
                    url: '/api/videos/purchase-management'
                },
                'inventory_management': {
                    title: '出入库管理演示',
                    url: '/api/videos/inventory-management'
                },
                'menu_management': {
                    title: '菜单管理演示',
                    url: '/api/videos/menu-management'
                },
                'inspection_system': {
                    title: '智能检查系统演示',
                    url: '/api/videos/inspection-system'
                },
                'family_dining': {
                    title: '家校共陪餐演示',
                    url: '/api/videos/family-dining'
                },
                // 新增的教学视频 (示例数据，需要后端API支持)
                'food_safety_tutorial': { // 使用不同的key区分演示和教学
                     title: '食品安全管理教学视频',
                     url: '/api/videos/tutorials/food-safety'
                },
                 'purchase_management_tutorial': {
                     title: '智能采购系统教学视频',
                     url: '/api/videos/tutorials/purchase-management'
                },
                 'inventory_management_tutorial': {
                     title: '出入库管理教学视频',
                     url: '/api/videos/tutorials/inventory-management'
                }
                // 更多教学视频...
            };

            // 主视频按钮点击事件
            document.getElementById('mainVideoBtn').addEventListener('click', function() {
                playVideo('main');
            });

            document.getElementById('videoThumbnail').addEventListener('click', function() {
                playVideo('main');
            });

            // 功能卡片演示视频按钮点击事件 (现有功能)
            document.querySelectorAll('.video-preview-btn').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const module = this.getAttribute('data-module');
                    playVideo(module);
                });
            });

            // 教学视频卡片播放按钮点击事件 (新增)
             document.querySelectorAll('.play-tutorial-video-btn').forEach(function(btn) {
                 btn.addEventListener('click', function(e) {
                     e.preventDefault();
                     // 从父级元素获取data-video-module
                     const card = this.closest('.tutorial-card');
                     if (card) {
                         const module = card.getAttribute('data-video-module');
                         // 调用 playVideo 函数，可能需要区分演示和教学视频的key
                         playVideo(module + '_tutorial'); // 假设教学视频的key是 module_tutorial
                     } else {
                         console.error('Could not find .tutorial-card parent element.');
                     }
                 });
             });


            // 播放视频函数 (已有的基础上进行完善)
            function playVideo(videoKey) {
                const video = videoData[videoKey];
                if (video) {
                    document.getElementById('videoModalLabel').textContent = video.title;

                    // **添加加载状态**
                    videoFrame.src = ''; // Clear previous video
                    const loadingHtml = '<div style="display:flex;align-items:center;justify-content:center;height:100%;background:#f8f9fa;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
                     // Use srcdoc for loading state to avoid navigating the iframe away
                     videoFrame.srcdoc = loadingHtml;

                    videoModal.show();

                    // Fetch the actual video URL
                    fetch(video.url)
                        .then(response => {
                            if (!response.ok) {
                                // Throw an error to be caught by the catch block
                                // Attempt to parse JSON error message, fallback to status text
                                return response.json().then(err => {
                                    throw new Error(`Failed to get video URL: ${err.message || response.statusText}`);
                                }).catch(() => { // Catch JSON parsing error
                                    throw new Error(`Failed to get video URL: ${response.statusText}`);
                                });
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (data.success && data.video_url) {
                                // Set the actual video source
                                videoFrame.srcdoc = ''; // Clear loading state
                                videoFrame.src = data.video_url;
                                // **TODO: Add event listeners to the iframe for loadeddata, error etc. for better feedback**
                            } else {
                                showVideoNotAvailable('服务器返回数据错误。'); // More specific message
                            }
                        })
                        .catch(error => {
                            console.error('Error loading video:', error);
                            showVideoNotAvailable(`视频加载失败: ${error.message}`); // Show error message
                        });
                } else {
                    showVideoNotAvailable('未找到对应的视频信息。'); // More specific message
                }
            }

            // 显示视频不可用提示 (完善提示信息) 改为使用 srcdoc
            function showVideoNotAvailable(message = '视频暂不可用') {
                document.getElementById('videoModalLabel').textContent = '视频无法播放';
                const errorHtml = `<div style="display:flex;align-items:center;justify-content:center;height:100%;background:#f8f9fa;color:#6c757d;font-family:Arial,sans-serif;"><div style="text-align:center;"><i style="font-size:3rem;margin-bottom:1rem;" class="fa fa-video-camera"></i><h4>${message}</h4><p>请检查网络连接或稍后再试，或联系技术支持。</p></div></div>`;
                videoFrame.srcdoc = errorHtml; // Use srcdoc for error message
                videoFrame.src = ''; // Ensure src is clear
                videoModal.show(); // Ensure modal is shown even on error
            }

            // 模态框关闭时停止视频播放 (已实现) - srcdoc clear is handled in playVideo
            document.getElementById('videoModal').addEventListener('hidden.bs.modal', function() {
                videoFrame.src = '';
                 videoFrame.srcdoc = ''; // Also clear srcdoc
            });

            // 功能卡片悬停效果 (已实现)
            document.querySelectorAll('.feature-card').forEach(function(card) {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.transition = 'transform 0.3s ease';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
             // 新增的教学卡片悬停效果
             document.querySelectorAll('.tutorial-card').forEach(function(card) {
                 card.addEventListener('mouseenter', function() {
                     this.style.transform = 'translateY(-5px)';
                     this.style.transition = 'transform 0.3s ease';
                 });

                 card.addEventListener('mouseleave', function() {
                     this.style.transform = 'translateY(0)';
                 });
             });

            // **TODO: Add more video player controls inside the modal iframe if needed**
        });

        // **TODO: Add JS for backToTop button if not already in the file**
        // (Checking file content shows backToTop JS is already present)
    </script>

    <!-- 自定义样式 -->
    <style nonce="{{ csp_nonce }}">
        /* 视频教学模块样式 (示例，可按需调整) */
        .tutorial-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer; /* Add cursor pointer */
        }

        .tutorial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }

        .tutorial-card .card-img-top {
            height: 180px; /* Adjust height as needed */
            object-fit: cover;
        }

        /* Add loading spinner style if needed */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .spinner-border {
            display: inline-block;
            width: 2rem;
            height: 2rem;
            vertical-align: -0.125em;
            border: 0.25em solid currentColor;
            border-right-color: transparent;
            border-radius: 50%;
            animation: .75s linear infinite spin;
        }
        .visually-hidden {
            position: absolute !important;
            width: 1px !important;
            height: 1px !important;
            padding: 0 !important;
            margin: -1px !important;
            overflow: hidden !important;
            clip: rect(0,0,0,0) !important;
            white-space: nowrap !important;
            border: 0 !important;
        }

        .video-thumbnail-container {
            position: relative;
            overflow: hidden;
        }

        .video-placeholder {
            transition: transform 0.3s ease;
        }

        .video-placeholder:hover {
            transform: scale(1.02);
        }

        .play-overlay {
            opacity: 0.9;
            transition: opacity 0.3s ease;
        }

        .video-thumbnail-container:hover .play-overlay {
            opacity: 1;
        }

        .play-button {
            transition: transform 0.3s ease;
        }

        .play-button:hover {
            transform: scale(1.1);
        }

        .feature-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }

        .video-preview-btn {
            transition: all 0.3s ease;
        }

        .video-preview-btn:hover {
            transform: translateY(-2px);
        }

        .bg-gradient {
            position: relative;
        }

        .bg-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .bg-gradient > .container {
            position: relative;
            z-index: 1;
        }
    </style>
</body>
</html>
