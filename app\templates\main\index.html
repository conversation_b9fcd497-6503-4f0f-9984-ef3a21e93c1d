<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台 - 暗夜霓红首页</title>
    <meta name="description" content="智慧食堂平台提供专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯">
    <style>
        body {
            min-height: 100vh;
            background: linear-gradient(135deg, #1a0025 0%, #2d0036 100%);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            letter-spacing: 0.02em;
        }
        .neon {
            color: #ff3cac;
            text-shadow: 0 0 8px #ff3cac, 0 0 16px #784ba0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .navbar {
            background: rgba(30,0,50,0.95);
            box-shadow: 0 2px 16px #ff3cac33;
            padding: 18px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .navbar-brand {
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            letter-spacing: 2px;
            text-shadow: 0 0 8px #ff3cac, 0 0 16px #784ba0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .navbar-nav {
            display: flex;
            gap: 32px;
        }
        .navbar-nav a {
            color: #fff;
            font-size: 1.1rem;
            font-weight: 500;
            text-decoration: none;
            transition: color 0.2s;
        }
        .navbar-nav a:hover {
            color: #ff3cac;
        }
        .navbar-actions {
            display: flex;
            gap: 16px;
        }
        .btn-neon {
            background: linear-gradient(90deg, #ff3cac 0%, #784ba0 100%);
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 28px;
            font-size: 1.1rem;
            font-weight: 700;
            box-shadow: 0 0 12px #ff3cac99;
            transition: background 0.2s, box-shadow 0.2s;
        }
        .btn-neon:hover {
            background: linear-gradient(90deg, #784ba0 0%, #ff3cac 100%);
            box-shadow: 0 0 24px #ff3caccc;
        }
        .hero {
            text-align: center;
            padding: 120px 0 60px 0;
        }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 900;
            margin-bottom: 18px;
            color: #fff;
            text-shadow: 0 0 16px #ff3cac, 0 0 32px #784ba0;
        }
        .hero-desc {
            font-size: 1.3rem;
            color: #ffb6e6;
            margin-bottom: 36px;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        .hero-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
        }
        .features-section {
            margin: 60px 0 40px 0;
        }
        .features-title {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-shadow: 0 0 8px #ff3cac;
        }
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 32px;
            margin-top: 32px;
        }
        .feature-card {
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 16px;
            box-shadow: 0 4px 24px #ff3cac22;
            padding: 32px 24px;
            text-align: center;
            color: #fff;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-8px) scale(1.03);
            box-shadow: 0 8px 32px #ff3cac55;
        }
        .feature-icon {
            font-size: 2.8rem;
            margin-bottom: 18px;
            color: #ff3cac;
            text-shadow: 0 0 8px #ff3cac;
        }
        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .feature-desc {
            color: #ffb6e6;
            font-size: 1rem;
        }
        .contact-section {
            margin: 60px 0 0 0;
            background: linear-gradient(135deg, #2d0036 60%, #ff3cac22 100%);
            border-radius: 16px;
            box-shadow: 0 4px 24px #ff3cac22;
            padding: 48px 24px;
            color: #fff;
        }
        .contact-title {
            font-size: 2rem;
            font-weight: 800;
            color: #ff3cac;
            margin-bottom: 18px;
            text-align: center;
            text-shadow: 0 0 8px #ff3cac;
        }
        .contact-info {
            text-align: center;
            color: #ffb6e6;
            font-size: 1.1rem;
            margin-bottom: 18px;
        }
        .contact-actions {
            display: flex;
            justify-content: center;
            gap: 24px;
        }
        .footer {
            text-align: center;
            color: #ffb6e6;
            padding: 32px 0 12px 0;
            font-size: 1rem;
            margin-top: 60px;
        }
        @media (max-width: 768px) {
            .hero { padding: 60px 0 30px 0; }
            .hero-title { font-size: 2.1rem; }
            .features-list { grid-template-columns: 1fr; }
            .features-section { margin: 30px 0 20px 0; }
            .contact-section { padding: 24px 8px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar">
            <div class="navbar-brand">
                <i class="fa fa-cutlery"></i> 智慧食堂平台
            </div>
            <div class="navbar-nav">
                <a href="#features">核心功能</a>
                <a href="#contact">联系我们</a>
            </div>
            <div class="navbar-actions">
                <a href="{{ url_for('auth.login') }}" class="btn-neon">登录</a>
                <a href="{{ url_for('auth.register') }}" class="btn-neon" style="background:linear-gradient(90deg,#ff3cac 0%,#784ba0 100%);color:#fff;">免费注册</a>
            </div>
        </nav>
        <section class="hero">
            <div class="hero-title neon">智慧食堂管理平台</div>
            <div class="hero-desc">专业的校园食堂管理解决方案，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。</div>
            <div class="hero-actions">
                <a href="{{ url_for('auth.register') }}" class="btn-neon"><i class="fa fa-user-plus me-2"></i>免费注册</a>
                <a href="#features" class="btn-neon" style="background:linear-gradient(90deg,#784ba0 0%,#ff3cac 100%);color:#fff;"><i class="fa fa-info-circle me-2"></i>了解更多</a>
            </div>
        </section>
        <section class="features-section" id="features">
            <div class="features-title">核心功能</div>
            <div class="features-list">
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-shield"></i></div>
                    <div class="feature-title">食品安全管理</div>
                    <div class="feature-desc">全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-shopping-cart"></i></div>
                    <div class="feature-title">智能采购系统</div>
                    <div class="feature-desc">智能价格对比，自动生成采购单，简化采购流程，提升采购效率。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-exchange"></i></div>
                    <div class="feature-title">出入库管理</div>
                    <div class="feature-desc">完整管理出入库流程，自动生成台账报表，实时监控库存情况。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-list"></i></div>
                    <div class="feature-title">灵活菜单管理</div>
                    <div class="feature-desc">自定义菜单，营养搭配推荐，满足不同需求。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-users"></i></div>
                    <div class="feature-title">师生用餐管理</div>
                    <div class="feature-desc">刷卡、扫码、预约多种用餐方式，数据实时统计。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-bar-chart"></i></div>
                    <div class="feature-title">财务报表分析</div>
                    <div class="feature-desc">自动生成多维度财务报表，辅助决策。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-cogs"></i></div>
                    <div class="feature-title">系统权限管理</div>
                    <div class="feature-desc">多级权限分配，安全高效。</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon"><i class="fa fa-cloud"></i></div>
                    <div class="feature-title">云端数据备份</div>
                    <div class="feature-desc">数据云端存储，安全可靠，随时恢复。</div>
                </div>
            </div>
        </section>
        <section class="contact-section" id="contact">
            <div class="contact-title">联系我们</div>
            <div class="contact-info">专业的技术团队为您提供7×24小时服务支持<br>电话：18373062333 &nbsp;|&nbsp; 邮箱：<EMAIL> &nbsp;|&nbsp; 湖南·岳阳</div>
            <div class="contact-actions">
                <a href="mailto:<EMAIL>" class="btn-neon"><i class="fa fa-envelope me-2"></i>邮件咨询</a>
                <a href="tel:18373062333" class="btn-neon"><i class="fa fa-phone me-2"></i>电话咨询</a>
            </div>
        </section>
        <div class="footer">
            <div>© 2025 智慧食堂平台. All rights reserved.</div>
            <div>专注校园食堂智能化管理解决方案</div>
        </div>
    </div>
</body>
</html>
