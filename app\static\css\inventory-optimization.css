/* 库存管理模块专用样式 */
/* 通用组件样式已移至 components.css */

/* 1. 库存状态统计样式 */
.status-stats {
    display: flex;
    gap: 20px;
    align-items: center;
}

.status-item {
    text-align: center;
    min-width: 60px;
}

.status-item small {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.status-item .badge {
    font-size: 0.875rem;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 32px;
}

/* 4. 食材信息突出显示 */
.ingredient-highlight {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.ingredient-category {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

.ingredient-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 6px;
    border: 2px solid #e9ecef;
}

/* 5. 紧凑按钮组 */
.btn-xs {
    padding: 2px 6px;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 4px;
    min-width: 28px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.btn-group-compact .btn {
    margin: 0;
    border-radius: 4px !important;
}

/* 6. 状态徽章优化 */
.badge-sm {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 8px;
    font-weight: 500;
}

/* 7. 可折叠筛选区域 */
.filter-collapse {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
}

.filter-collapse .form-control-sm {
    font-size: 0.875rem;
    padding: 4px 8px;
    height: auto;
}

/* 8. 食材卡片样式（用于详细视图） */
.ingredient-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s ease;
}

.ingredient-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.ingredient-card .ingredient-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.ingredient-card .ingredient-details {
    font-size: 0.875rem;
    color: #6c757d;
}

.ingredient-card .ingredient-stock {
    font-size: 1.2rem;
    font-weight: 700;
    color: #28a745;
}

/* 9. 库存状态指示器 */
.stock-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.stock-indicator.sufficient {
    background-color: #28a745;
}

.stock-indicator.low {
    background-color: #ffc107;
}

.stock-indicator.critical {
    background-color: #dc3545;
}

.stock-indicator.expired {
    background-color: #6c757d;
}

/* 10. 响应式优化 */
@media (max-width: 768px) {
    .compact-toolbar {
        flex-direction: column;
        gap: 8px;
    }
    
    .status-stats {
        justify-content: space-around;
        width: 100%;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .btn-xs {
        padding: 1px 4px;
        font-size: 0.7rem;
        min-width: 24px;
        height: 20px;
    }
}

/* 11. 打印样式优化 */
@media print {
    .compact-toolbar,
    .filter-collapse,
    .btn-group-compact {
        display: none !important;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .ingredient-highlight {
        font-weight: bold;
    }
}

/* 12. 用友主题适配 */
[data-theme="yonyou"] .compact-toolbar {
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    border-color: #E0E0E0;
}

[data-theme="yonyou"] .ingredient-card {
    background-color: #FFFFFF;
    border-color: #E0E0E0;
    color: #212121;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

[data-theme="yonyou"] .ingredient-card:hover {
    border-color: #1E88E5;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.15);
}

[data-theme="yonyou"] .table-compact th {
    background-color: #E3F2FD;
    color: #1565C0;
    border: 1px solid #BBDEFB;
}

[data-theme="yonyou"] .table-compact tbody tr:hover {
    background-color: #E3F2FD;
}
