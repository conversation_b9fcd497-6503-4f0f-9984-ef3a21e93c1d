/* 
 * 统一组件样式文件
 * 包含所有通用组件的样式定义
 * 避免重复和冲突
 */

/* === 通用工具栏组件 === */
.compact-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.compact-toolbar .btn {
    font-size: var(--font-size-base, 0.875rem);
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 8px;
    transition: all 0.2s ease;
}

.compact-toolbar .btn:last-child {
    margin-right: 0;
}

/* === 筛选区域组件 === */
.filter-collapse {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.filter-collapse .form-control-sm {
    font-size: 0.875rem;
    padding: 4px 8px;
    height: auto;
}

/* === 按钮组件 === */
.btn-xs {
    padding: 4px 8px;
    font-size: var(--font-size-sm, 0.75rem);
    line-height: 1.2;
    border-radius: 4px;
    min-width: 32px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-group-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.btn-group-compact .btn {
    margin: 0;
    border-radius: 4px !important;
}

/* === 表格组件 === */
.table-compact {
    font-size: var(--font-size-base, 0.875rem);
    margin-bottom: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-compact th {
    font-size: var(--font-size-md, 1rem);
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-dark, #1d4ed8) 100%);
    border-bottom: 2px solid var(--theme-primary-dark, #1d4ed8);
    padding: 10px 12px;
    white-space: nowrap;
    vertical-align: middle;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
}

.table-compact td {
    padding: 10px 12px;
    vertical-align: middle;
    border-bottom: 1px solid rgba(var(--theme-primary-rgb, 37, 99, 235), 0.1);
    font-size: var(--font-size-base, 0.875rem);
    transition: all 0.2s ease;
}

.table-compact tbody tr {
    transition: all 0.2s ease;
}

.table-compact tbody tr:hover {
    background: linear-gradient(135deg,
        rgba(var(--theme-primary-rgb, 37, 99, 235), 0.03) 0%,
        rgba(var(--theme-primary-rgb, 37, 99, 235), 0.08) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb, 37, 99, 235), 0.1);
}

/* === 徽章组件 === */
.badge-sm {
    font-size: var(--font-size-sm, 0.75rem);
    padding: 4px 8px;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* === 状态指示器组件 === */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.danger {
    background-color: #dc3545;
}

.status-indicator.secondary {
    background-color: #6c757d;
}

/* === 卡片组件 === */
.info-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.info-card .card-title {
    font-size: var(--font-size-lg, 1.125rem);
    font-weight: 600;
    color: var(--theme-primary, #2c3e50);
    margin-bottom: 6px;
    line-height: 1.3;
}

.info-card .card-subtitle {
    font-size: var(--font-size-base, 0.875rem);
    color: var(--theme-gray-600, #6c757d);
    line-height: 1.4;
}

/* === 高亮文本组件 === */
.text-highlight {
    font-weight: 600;
    color: var(--theme-primary, #2c3e50);
    font-size: var(--font-size-base, 0.875rem);
    line-height: 1.4;
}

.text-category {
    font-size: var(--font-size-sm, 0.75rem);
    color: var(--theme-gray-600, #6c757d);
    background-color: var(--theme-gray-100, #f8f9fa);
    padding: 3px 8px;
    border-radius: 6px;
    display: inline-block;
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .compact-toolbar {
        flex-direction: column;
        gap: 8px;
        padding: 10px 12px;
    }
    
    .compact-toolbar .btn {
        margin-right: 0;
        margin-bottom: 4px;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .table-compact th,
    .table-compact td {
        padding: 6px 8px;
    }
    
    .btn-xs {
        padding: 1px 4px;
        font-size: 0.7rem;
        min-width: 24px;
        height: 20px;
    }
    
    .info-card {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .compact-toolbar {
        padding: 8px 10px;
    }
    
    .table-compact {
        font-size: 0.75rem;
    }
    
    .btn-xs {
        font-size: 0.65rem;
        min-width: 20px;
        height: 18px;
    }
}

/* === 打印样式 === */
@media print {
    .compact-toolbar,
    .filter-collapse,
    .btn-group-compact {
        display: none !important;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .text-highlight {
        font-weight: bold;
    }
    
    .info-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* === 表格主题变体 === */

/* 专业蓝色表格主题 */
.table-theme-professional .table-compact th {
    background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
    color: white;
    border-bottom: 2px solid #1e3a8a;
}

/* 成功绿色表格主题 */
.table-theme-success .table-compact th {
    background: linear-gradient(135deg, #047857 0%, #059669 100%);
    color: white;
    border-bottom: 2px solid #047857;
}

/* 警告橙色表格主题 */
.table-theme-warning .table-compact th {
    background: linear-gradient(135deg, #b45309 0%, #d97706 100%);
    color: white;
    border-bottom: 2px solid #b45309;
}

/* 信息青色表格主题 */
.table-theme-info .table-compact th {
    background: linear-gradient(135deg, #0e7490 0%, #0891b2 100%);
    color: white;
    border-bottom: 2px solid #0e7490;
}

/* 危险红色表格主题 */
.table-theme-danger .table-compact th {
    background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
    color: white;
    border-bottom: 2px solid #b91c1c;
}

/* === 主题适配 === */
[data-theme="yonyou"] .compact-toolbar {
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    border-color: #E0E0E0;
}

[data-theme="yonyou"] .info-card {
    background-color: #FFFFFF;
    border-color: #E0E0E0;
    color: #212121;
}

[data-theme="yonyou"] .info-card:hover {
    border-color: #1E88E5;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.15);
}

[data-theme="yonyou"] .table-compact th {
    background: linear-gradient(135deg, #1E88E5 0%, #1565C0 100%);
    color: white;
    border-bottom: 2px solid #1565C0;
}

[data-theme="yonyou"] .table-compact tbody tr:hover {
    background: linear-gradient(135deg,
        rgba(30, 136, 229, 0.03) 0%,
        rgba(30, 136, 229, 0.08) 100%);
}
