/* 
 * 统一组件样式文件
 * 包含所有通用组件的样式定义
 * 避免重复和冲突
 */

/* === 通用工具栏组件 === */
.compact-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.compact-toolbar .btn {
    font-size: 0.875rem;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 500;
    margin-right: 8px;
}

.compact-toolbar .btn:last-child {
    margin-right: 0;
}

/* === 筛选区域组件 === */
.filter-collapse {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.filter-collapse .form-control-sm {
    font-size: 0.875rem;
    padding: 4px 8px;
    height: auto;
}

/* === 按钮组件 === */
.btn-xs {
    padding: 2px 6px;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 4px;
    min-width: 28px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-compact {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
}

.btn-group-compact .btn {
    margin: 0;
    border-radius: 4px !important;
}

/* === 表格组件 === */
.table-compact {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.table-compact th {
    font-size: 0.8rem;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    padding: 8px 12px;
    white-space: nowrap;
    vertical-align: middle;
}

.table-compact td {
    padding: 8px 12px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table-compact tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* === 徽章组件 === */
.badge-sm {
    font-size: 0.7rem;
    padding: 3px 6px;
    border-radius: 8px;
    font-weight: 500;
}

/* === 状态指示器组件 === */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.danger {
    background-color: #dc3545;
}

.status-indicator.secondary {
    background-color: #6c757d;
}

/* === 卡片组件 === */
.info-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    background: white;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.info-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.info-card .card-subtitle {
    font-size: 0.875rem;
    color: #6c757d;
}

/* === 高亮文本组件 === */
.text-highlight {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.text-category {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .compact-toolbar {
        flex-direction: column;
        gap: 8px;
        padding: 10px 12px;
    }
    
    .compact-toolbar .btn {
        margin-right: 0;
        margin-bottom: 4px;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .table-compact th,
    .table-compact td {
        padding: 6px 8px;
    }
    
    .btn-xs {
        padding: 1px 4px;
        font-size: 0.7rem;
        min-width: 24px;
        height: 20px;
    }
    
    .info-card {
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .compact-toolbar {
        padding: 8px 10px;
    }
    
    .table-compact {
        font-size: 0.75rem;
    }
    
    .btn-xs {
        font-size: 0.65rem;
        min-width: 20px;
        height: 18px;
    }
}

/* === 打印样式 === */
@media print {
    .compact-toolbar,
    .filter-collapse,
    .btn-group-compact {
        display: none !important;
    }
    
    .table-compact {
        font-size: 0.8rem;
    }
    
    .text-highlight {
        font-weight: bold;
    }
    
    .info-card {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* === 主题适配 === */
[data-theme="yonyou"] .compact-toolbar {
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
    border-color: #E0E0E0;
}

[data-theme="yonyou"] .info-card {
    background-color: #FFFFFF;
    border-color: #E0E0E0;
    color: #212121;
}

[data-theme="yonyou"] .info-card:hover {
    border-color: #1E88E5;
    box-shadow: 0 2px 8px rgba(30, 136, 229, 0.15);
}

[data-theme="yonyou"] .table-compact th {
    background-color: #E3F2FD;
    color: #1565C0;
    border: 1px solid #BBDEFB;
}

[data-theme="yonyou"] .table-compact tbody tr:hover {
    background-color: #E3F2FD;
}
