/* 用友NC Cloud/U8 财务风格主题 - 与全局用友主题集成 */

/* 当全局使用用友主题时，财务模块自动应用相同样式 */
[data-theme="yonyou"] .financial-container,
[data-theme="yonyou"] .financial-content,
[data-theme="yonyou"] .voucher-edit-container {
  background: var(--theme-surface) !important;
  font-family: var(--yonyou-font-family);
  color: var(--theme-text);
  line-height: 1.5; /* 调整行高 */
}

[data-theme="yonyou"] .financial-card,
[data-theme="yonyou"] .voucher-header-section,
[data-theme="yonyou"] .voucher-table-section {
  background: var(--theme-card-bg);
  border: 1px solid var(--theme-border);
  box-shadow: var(--yonyou-card-shadow);
}

[data-theme="yonyou"] .financial-table th {
  background-color: var(--theme-surface-dark);
  color: var(--theme-primary);
  border: 1px solid var(--theme-border);
}

[data-theme="yonyou"] .financial-btn-primary {
  background-color: var(--theme-primary);
  color: white;
}

[data-theme="yonyou"] .financial-form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

:root {
  /* 主色调 - 更新为更现代的蓝色 */
  --yy-primary: #2563eb;
  --yy-primary-hover: #1d4ed8;
  --yy-primary-active: #1e40af;
  --yy-success: #16a34a;
  --yy-warning: #d97706;
  --yy-danger: #dc2626;
  --yy-info: #0284c7;
  
  /* 中性色 - 优化灰度 */
  --yy-bg: #f8fafc;
  --yy-table-header: #f1f5f9;
  --yy-border: #e2e8f0;
  --yy-border-light: #f1f5f9;
  --yy-text: #1e293b;
  --yy-text-secondary: #475569;
  --yy-text-light: #64748b;
  
  /* 尺寸 - 增加圆角 */
  --yy-radius: 6px;
  --yy-radius-lg: 8px;
  --yy-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
  --yy-shadow-lg: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
  --yy-shadow-hover: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
  
  /* 字体 */
  --yy-font-family: 'Microsoft YaHei', '思源黑体', Arial, sans-serif;
  --yy-font-size-base: 14px;
  --yy-font-size-sm: 13px;
  --yy-font-size-lg: 15px;
  --yy-line-height: 1.6;
  
  /* 图标 */
  --yy-icon-size: 16px;
  --yy-icon-size-lg: 20px;
  --yy-icon-size-sm: 14px;
  
  /* 动画 */
  --yy-transition: all 0.2s ease-in-out;
}

body, .financial-content, .voucher-edit-container {
  background: var(--yy-bg) !important;
  font-family: var(--yy-font-family);
  color: var(--yy-text);
  line-height: var(--yy-line-height);
}

.card, .financial-card, .voucher-header-section, .voucher-table-section, .voucher-signature-section {
  background: #fff;
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  border: 1px solid var(--yy-border);
  transition: var(--yy-transition);
}

.card:hover, .financial-card:hover {
  box-shadow: var(--yy-shadow-hover);
  transform: translateY(-1px);
}

.card-header, .financial-card-header, .voucher-header-title {
  background: var(--yy-table-header);
  border-bottom: 1px solid var(--yy-border);
  font-weight: bold;
  color: #222;
  font-size: 1.1rem;
  line-height: 1.5; /* 调整行高 */
}

.btn, .btn-group .btn {
  background: var(--yy-primary);
  color: #fff;
  border: none;
  border-radius: var(--yy-radius);
  padding: 6px 18px;
  font-size: 1.1em;
  transition: background 0.2s;
}
.btn:hover, .btn:focus {
  background: #40a9ff;
  color: #fff;
}
.btn-secondary, .btn-outline-secondary {
  background: #f0f2f5;
  color: #222;
  border: 1px solid var(--yy-border);
}
.btn-secondary:hover, .btn-outline-secondary:hover {
  background: #e6f7ff;
  color: var(--yy-primary);
}

.table, .financial-table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  font-size: 11px;
  border: 1px solid var(--yy-border);
}

.table th, .financial-table th {
  background: var(--yy-table-header);
  color: var(--yy-text);
  font-weight: bold;
  border: 1px solid var(--yy-border);
  padding: 12px 8px;
  text-align: center;
  font-size: 11px;
  position: relative;
  line-height: 1.5;
}

.table th::after, .financial-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--yy-border);
}

.table td, .financial-table td {
  border: 1px solid var(--yy-border);
  padding: 10px 8px;
  text-align: center;
  background: #fff;
  font-size: 11px;
  line-height: 1.5;
}

.table tbody tr:hover, .financial-table tbody tr:hover {
  background: var(--yy-table-header);
}

/* 表格边框样式 */
.table-bordered, .financial-table {
  border: 1px solid var(--yy-border);
}

.table-bordered th, .financial-table th {
  border: 1px solid var(--yy-border);
}

.table-bordered td, .financial-table td {
  border: 1px solid var(--yy-border);
}

.form-control, .form-select, input[type="text"], input[type="date"], input[type="number"], select {
  border-radius: var(--yy-radius);
  border: 1px solid var(--yy-border);
  font-size: 1rem;
  padding: 6px 10px;
  background: #fff;
  transition: border 0.2s;
  line-height: 1.5; /* 调整行高 */
}
.form-control:focus, .form-select:focus, input:focus, select:focus {
  border-color: var(--yy-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.1);
}

.financial-stats, .stats-bar {
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  margin-bottom: 1rem;
  padding: 1rem 2rem;
  line-height: 1.5; /* 调整行高 */
}
.financial-stat-item, .stats-item {
  text-align: center;
  flex: 1;
  color: #222;
}
.financial-stat-value, .stats-value {
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--yy-primary);
}
.financial-stat-label, .stats-label {
  font-size: 1.1rem;
  color: #888;
}

.breadcrumb, .financial-breadcrumb {
  background: transparent;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5; /* 调整行高 */
}
.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #bbb;
}

.badge {
  border-radius: var(--yy-radius);
  padding: 2px 10px;
  font-size: 1.1rem;
  font-weight: normal;
  line-height: 1.5; /* 调整行高 */
}
.badge-secondary { background: #f5f7fa; color: #888; }
.badge-warning { background: #fffbe6; color: #faad14; }
.badge-success { background: #f6ffed; color: #52c41a; }
.badge-primary { background: #e6f7ff; color: #1890ff; }

.voucher-actions, .financial-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

/* 输入区紧凑风格 */
.voucher-header-form .form-label, .form-label {
  color: #888;
  font-size: 1.1rem;
  margin-bottom: 0.2rem;
  line-height: 1.5; /* 调整行高 */
}
.voucher-header-form .form-control-sm, .form-control-sm {
  font-size: 1.1rem;
  padding: 4px 8px;
  border-radius: var(--yy-radius);
  line-height: 1.5; /* 调整行高 */
}

/* 统计金额、数字高亮 */
.amount-cell, .stats-value, .financial-amount {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 1.1rem;
  color: var(--yy-primary);
  text-align: right;
  line-height: 1.5; /* 调整行高 */
}

/* 模态框风格 */
.modal-content {
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .financial-stats, .stats-bar {
    flex-direction: column;
    padding: 1rem;
  }
  .voucher-header-section, .voucher-table-section {
    padding: 0.5rem;
  }
  .voucher-actions, .financial-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* UFIDA NC Cloud/U8 Style Theme */

:root {
    /* 主色调 */
    --yonyou-primary: #1E88E5; /* 蓝色，专业可靠 */
    --yonyou-secondary: #64B5F6;
    --yonyou-success: #43A047; /* 绿色，自然健康 */
    --yonyou-warning: #FFA000;
    --yonyou-danger: #E53935;
    --yonyou-info: #039BE5;
    
    /* 中性色 */
    --yonyou-gray-100: #F5F5F5;
    --yonyou-gray-200: #EEEEEE;
    --yonyou-gray-300: #E0E0E0;
    --yonyou-gray-400: #BDBDBD;
    --yonyou-gray-500: #9E9E9E;
    --yonyou-gray-600: #757575;
    --yonyou-gray-700: #616161;
    --yonyou-gray-800: #424242;
    --yonyou-gray-900: #212121;
    
    /* 字体 */
    --yonyou-font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

/* 基础样式重置 */
.financial-container {
    font-family: var(--yonyou-font-family);
    color: var(--yonyou-gray-800);
    background-color: #fff;
    line-height: 1.5; /* 调整行高 */
}

/* 面包屑导航 */
.financial-breadcrumb {
    background-color: var(--yonyou-gray-100);
    padding: 8px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    line-height: 1.5; /* 调整行高 */
}

.financial-breadcrumb .breadcrumb {
    margin: 0;
    padding: 0;
}

.financial-breadcrumb .breadcrumb-item a {
    color: var(--yonyou-primary);
    text-decoration: none;
}

.financial-breadcrumb .breadcrumb-item.active {
    color: var(--yonyou-gray-600);
}

/* 页面标题 */
.financial-page-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--yonyou-gray-900);
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--yonyou-primary);
    line-height: 1.5; /* 调整行高 */
}

/* 按钮样式 */
.financial-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.financial-btn-primary {
    background-color: var(--yonyou-primary);
    color: white;
}

.financial-btn-primary:hover {
    background-color: #1976D2;
}

.financial-btn-secondary {
    background-color: var(--yonyou-gray-200);
    color: var(--yonyou-gray-800);
}

.financial-btn-secondary:hover {
    background-color: var(--yonyou-gray-300);
}

/* 表单样式 */
.financial-form-group {
    margin-bottom: 16px;
}

.financial-form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--yonyou-gray-700);
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--yonyou-gray-300);
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    line-height: 1.5; /* 调整行高 */
}

.financial-form-control:focus {
    border-color: var(--yonyou-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

/* 状态标签 */
.financial-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-status.draft {
    background-color: var(--yonyou-gray-200);
    color: var(--yonyou-gray-700);
}

.financial-status.pending {
    background-color: var(--yonyou-warning);
    color: white;
}

.financial-status.approved {
    background-color: var(--yonyou-success);
    color: white;
}

.financial-status.posted {
    background-color: var(--yonyou-primary);
    color: white;
}

/* 金额显示 */
.financial-amount {
    font-family: "Consolas", monospace;
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-amount.positive {
    color: var(--yonyou-success);
}

.financial-amount.negative {
    color: var(--yonyou-danger);
}

.financial-amount.zero {
    color: var(--yonyou-gray-600);
}

/* 卡片样式 */
.financial-card {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.financial-card-header {
    padding: 16px;
    border-bottom: 1px solid var(--yonyou-gray-200);
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-card-body {
    padding: 16px;
    line-height: 1.5; /* 调整行高 */
}

/* 搜索区域 */
.financial-search {
    background-color: var(--yonyou-gray-100);
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 24px;
    line-height: 1.5; /* 调整行高 */
}

.financial-search-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
}

.financial-search-col {
    padding: 8px;
    flex: 1;
    min-width: 200px;
}

/* 分页样式 */
.financial-pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

.financial-pagination .page-item {
    margin: 0 4px;
}

.financial-pagination .page-link {
    padding: 6px 12px;
    border: 1px solid var(--yonyou-gray-300);
    border-radius: 4px;
    color: var(--yonyou-gray-700);
    text-decoration: none;
    line-height: 1.5; /* 调整行高 */
}

.financial-pagination .page-link:hover {
    background-color: var(--yonyou-gray-100);
}

.financial-pagination .active .page-link {
    background-color: var(--yonyou-primary);
    border-color: var(--yonyou-primary);
    color: white;
}

/* 弹窗样式 */
.financial-modal {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.financial-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--yonyou-gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1.5; /* 调整行高 */
}

.financial-modal-body {
    padding: 24px;
    overflow-y: auto;
    line-height: 1.5; /* 调整行高 */
}

.financial-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--yonyou-gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    line-height: 1.5; /* 调整行高 */
}

/* 加载动画 */
.financial-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--yonyou-gray-300);
    border-radius: 50%;
    border-top-color: var(--yonyou-primary);
    animation: financial-spin 1s linear infinite;
}

@keyframes financial-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式布局 */
@media (max-width: 768px) {
    .financial-search-col {
        min-width: 100%;
    }
    
    .financial-table {
        display: block;
        overflow-x: auto;
    }
}

/* 新增：表格样式优化 */
.financial-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin-bottom: 1rem;
}

.financial-table th {
  background: linear-gradient(to bottom, #f0f7ff, #e6f7ff);
  color: var(--yy-primary);
  font-weight: 600;
  padding: 12px 16px;
  border-bottom: 2px solid var(--yy-primary);
  text-align: left;
  line-height: 1.5; /* 调整行高 */
}

.financial-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--yy-border-light);
  transition: background-color 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-table tbody tr:hover td {
  background-color: #f0f7ff;
}

/* 新增：表单样式优化 */
.financial-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
}

.financial-form-group {
  margin-bottom: 24px;
}

.financial-form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text-secondary);
  font-weight: 500;
  line-height: 1.5; /* 调整行高 */
}

.financial-form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-form-control:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

/* 新增：按钮样式优化 */
.financial-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
  border: none;
}

.financial-btn-primary {
  background: var(--yy-primary);
  color: #fff;
}

.financial-btn-primary:hover {
  background: var(--yy-primary-hover);
}

.financial-btn-primary:active {
  background: var(--yy-primary-active);
}

.financial-btn-secondary {
  background: #fff;
  color: var(--yy-text);
  border: 1px solid var(--yy-border);
}

.financial-btn-secondary:hover {
  color: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-btn-success {
  background: var(--yy-success);
  color: #fff;
}

.financial-btn-success:hover {
  background: #73d13d;
}

.financial-btn-warning {
  background: var(--yy-warning);
  color: #fff;
}

.financial-btn-warning:hover {
  background: #ffc53d;
}

.financial-btn-danger {
  background: var(--yy-danger);
  color: #fff;
}

.financial-btn-danger:hover {
  background: #ff4d4f;
}

.financial-btn-text {
  background: transparent;
  color: var(--yy-text);
  padding: 4px 8px;
}

.financial-btn-text:hover {
  color: var(--yy-primary);
  background: rgba(24,144,255,0.1);
}

.financial-btn-link {
  background: transparent;
  color: var(--yy-primary);
  padding: 4px 8px;
}

.financial-btn-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-btn-sm {
  padding: 4px 12px;
  font-size: var(--yy-font-size-sm);
}

.financial-btn-lg {
  padding: 12px 20px;
  font-size: var(--yy-font-size-lg);
}

.financial-btn-block {
  width: 100%;
}

.financial-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮组样式 */
.financial-btn-group {
  display: inline-flex;
  border-radius: var(--yy-radius);
  overflow: hidden;
}

.financial-btn-group .financial-btn {
  border-radius: 0;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.financial-btn-group .financial-btn:first-child {
  border-top-left-radius: var(--yy-radius);
  border-bottom-left-radius: var(--yy-radius);
}

.financial-btn-group .financial-btn:last-child {
  border-top-right-radius: var(--yy-radius);
  border-bottom-right-radius: var(--yy-radius);
  border-right: none;
}

/* 新增：徽标样式 */
.financial-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: var(--yy-font-size-sm);
  line-height: 20px;
  border-radius: 10px;
  background: var(--yy-danger);
  color: #fff;
  font-weight: normal;
}

.financial-badge-dot {
  min-width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
}

.financial-badge-count {
  position: relative;
  display: inline-block;
}

.financial-badge-count .financial-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transform: translate(50%, -50%);
}

/* 新增：标签样式 */
.financial-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: var(--yy-font-size-sm);
  line-height: 1.5;
  border-radius: var(--yy-radius);
  background: var(--yy-table-header);
  color: var(--yy-text);
  margin-right: 8px;
  margin-bottom: 8px;
}

.financial-tag-primary {
  background: #e6f7ff;
  color: var(--yy-primary);
  border: 1px solid #91d5ff;
}

.financial-tag-success {
  background: #f6ffed;
  color: var(--yy-success);
  border: 1px solid #b7eb8f;
}

.financial-tag-warning {
  background: #fffbe6;
  color: var(--yy-warning);
  border: 1px solid #ffe58f;
}

.financial-tag-danger {
  background: #fff1f0;
  color: var(--yy-danger);
  border: 1px solid #ffa39e;
}

.financial-tag-close {
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-tag-close:hover {
  color: var(--yy-text);
}

/* 新增：分割线样式 */
.financial-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 16px 0;
  background: var(--yy-border-light);
}

.financial-divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  margin: 0 8px;
  vertical-align: middle;
  background: var(--yy-border-light);
}

.financial-divider-text {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: var(--yy-text-secondary);
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  white-space: nowrap;
}

.financial-divider-text::before,
.financial-divider-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--yy-border-light);
}

.financial-divider-text::before {
  margin-right: 16px;
}

.financial-divider-text::after {
  margin-left: 16px;
}

/* 新增：表单组件样式 */
.financial-form-item {
  margin-bottom: 24px;
}

.financial-form-item-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text);
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
}

.financial-form-item-label-required::before {
  content: '*';
  color: var(--yy-danger);
  margin-right: 4px;
}

.financial-form-item-help {
  margin-top: 4px;
  color: var(--yy-text-light);
  font-size: var(--yy-font-size-sm);
}

.financial-form-item-error {
  margin-top: 4px;
  color: var(--yy-danger);
  font-size: var(--yy-font-size-sm);
}

/* 新增：输入框样式 */
.financial-input {
  display: inline-flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-input-disabled {
  background: var(--yy-table-header);
  cursor: not-allowed;
  color: var(--yy-text-light);
}

.financial-input-error {
  border-color: var(--yy-danger);
}

.financial-input-error:focus {
  box-shadow: 0 0 0 2px rgba(245,34,45,0.2);
}

.financial-input-prefix,
.financial-input-suffix {
  display: flex;
  align-items: center;
  color: var(--yy-text-light);
}

.financial-input-prefix {
  margin-right: 8px;
}

.financial-input-suffix {
  margin-left: 8px;
}

/* 新增：文本域样式 */
.financial-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  resize: vertical;
}

.financial-textarea:hover {
  border-color: var(--yy-primary-hover);
}

.financial-textarea:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

/* 新增：选择器样式 */
.financial-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-select-selector {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
}

.financial-select-selector:hover {
  border-color: var(--yy-primary-hover);
}

.financial-select-selector:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  padding: 4px 0;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

.financial-select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.financial-select-option:hover {
  background: var(--yy-table-header);
}

.financial-select-option-selected {
  background: #e6f7ff;
  color: var(--yy-primary);
}

/* 新增：日期选择器样式 */
.financial-datepicker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-datepicker-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-datepicker-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-datepicker-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-datepicker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  padding: 12px;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

/* 新增：单选框样式 */
.financial-radio {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-radio-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-radio-input:checked + .financial-radio-inner {
  border-color: var(--yy-primary);
}

.financial-radio-input:checked + .financial-radio-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--yy-primary);
  border-radius: 50%;
}

/* 新增：复选框样式 */
.financial-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-checkbox-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-checkbox-input:checked + .financial-checkbox-inner {
  background: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-checkbox-input:checked + .financial-checkbox-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 4px;
  height: 8px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}

/* 新增：开关样式 */
.financial-switch {
  position: relative;
  display: inline-block;
  min-width: 44px;
  height: 22px;
  cursor: pointer;
}

.financial-switch-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-switch-inner {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  background: var(--yy-border);
  border-radius: 11px;
  transition: all 0.3s;
}

.financial-switch-inner::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-switch-input:checked + .financial-switch-inner {
  background: var(--yy-primary);
}

.financial-switch-input:checked + .financial-switch-inner::after {
  left: calc(100% - 20px);
}

/* 新增：上传组件样式 */
.financial-upload {
  display: inline-block;
  cursor: pointer;
}

.financial-upload-input {
  display: none;
}

.financial-upload-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  color: var(--yy-text);
  background: #fff;
  border: 1px dashed var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-upload-trigger:hover {
  border-color: var(--yy-primary);
  color: var(--yy-primary);
}

.financial-upload-list {
  margin-top: 8px;
}

.financial-upload-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  margin-bottom: 8px;
}

.financial-upload-item-name {
  flex: 1;
  margin: 0 8px;
  color: var(--yy-text);
}

.financial-upload-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.financial-upload-item-action {
  color: var(--yy-text-light);
  cursor: pointer;
  transition: all 0.3s;
}

.financial-upload-item-action:hover {
  color: var(--yy-primary);
}

/* 新增：数据卡片组件 */
.financial-data-card {
  background: #fff;
  border-radius: var(--yy-radius);
  padding: 20px;
  box-shadow: var(--yy-shadow);
  transition: var(--yy-transition);
}

.financial-data-card:hover {
  box-shadow: var(--yy-shadow-hover);
  transform: translateY(-2px);
}

.financial-data-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.financial-data-card-title {
  font-size: var(--yy-font-size-base);
  color: var(--yy-text-secondary);
  font-weight: 500;
}

.financial-data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--yy-text);
  font-family: 'Consolas', monospace;
}

/* 新增：状态指示器 */
.financial-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: var(--yy-font-size-sm);
  font-weight: 500;
  transition: var(--yy-transition);
}

.financial-status.draft {
  background: #f1f5f9;
  color: var(--yy-text-secondary);
}

.financial-status.pending {
  background: #fef3c7;
  color: var(--yy-warning);
}

.financial-status.approved {
  background: #dcfce7;
  color: var(--yy-success);
}

.financial-status.posted {
  background: #dbeafe;
  color: var(--yy-primary);
}

/* 新增：金额显示优化 */
.financial-amount {
  font-family: 'Consolas', monospace;
  font-weight: 500;
  text-align: right;
}

.financial-amount.positive {
  color: var(--yy-success);
}

.financial-amount.negative {
  color: var(--yy-danger);
}

.financial-amount.zero {
  color: var(--yy-text-light);
}

/* 新增：响应式布局优化 */
@media (max-width: 768px) {
  .financial-data-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .financial-table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .financial-form {
    padding: 16px;
  }
  
  .financial-card-header,
  .financial-card-body {
    padding: 16px;
  }
}

/* 新增：页面布局基础样式 */
.financial-layout {
  display: flex;
  min-height: 100vh;
  background: var(--yy-bg);
}

/* 新增：侧边导航样式 */
.financial-sider {
  width: 240px;
  background: #fff;
  border-right: 1px solid var(--yy-border);
  box-shadow: var(--yy-shadow);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.3s;
}

.financial-sider-header {
  height: 64px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--yy-border-light);
  line-height: 1.5; /* 调整行高 */
}

.financial-sider-menu {
  padding: 16px 0;
}

.financial-menu-item {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  color: var(--yy-text);
  cursor: pointer;
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-menu-item:hover {
  background: var(--yy-table-header);
  color: var(--yy-primary);
}

.financial-menu-item.active {
  background: #e6f7ff;
  color: var(--yy-primary);
  border-right: 3px solid var(--yy-primary);
}

.financial-menu-item i {
  margin-right: 12px;
  font-size: 16px;
}

/* 新增：主内容区样式 */
.financial-main {
  flex: 1;
  margin-left: 240px;
  padding: 24px;
  transition: all 0.3s;
}

/* 新增：顶部工具栏样式 */
.financial-toolbar {
  background: #fff;
  padding: 16px 24px;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.financial-toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 新增：工作区样式 */
.financial-workspace {
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
  padding: 24px;
}

.financial-workspace-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--yy-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-workspace-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--yy-text);
}

/* 新增：数据展示区样式 */
.financial-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.financial-data-card {
  background: #fff;
  border-radius: var(--yy-radius);
  padding: 20px;
  box-shadow: var(--yy-shadow);
}

.financial-data-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  line-height: 1.5; /* 调整行高 */
}

.financial-data-card-title {
  font-size: 14px;
  color: var(--yy-text-secondary);
}

.financial-data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--yy-text);
}

/* 新增：操作按钮组样式 */
.financial-action-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.financial-action-btn {
  padding: 6px 12px;
  border-radius: var(--yy-radius);
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-action-btn i {
  font-size: 14px;
}

/* 新增：表格工具栏样式 */
.financial-table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--yy-table-header);
  border-radius: var(--yy-radius);
  line-height: 1.5; /* 调整行高 */
}

.financial-table-toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.financial-table-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 新增：批量操作工具栏样式 */
.financial-batch-toolbar {
  position: fixed;
  bottom: 0;
  left: 240px;
  right: 0;
  background: #fff;
  padding: 12px 24px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  line-height: 1.5; /* 调整行高 */
}

.financial-batch-toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.financial-batch-toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 新增：弹窗样式优化 */
.financial-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.financial-modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--yy-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--yy-text);
}

.financial-modal-body {
  padding: 24px;
  overflow-y: auto;
  line-height: 1.5; /* 调整行高 */
}

.financial-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--yy-border-light);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  line-height: 1.5; /* 调整行高 */
}

/* 新增：响应式布局优化 */
@media (max-width: 1200px) {
  .financial-sider {
    width: 200px;
  }
  
  .financial-main {
    margin-left: 200px;
  }
  
  .financial-batch-toolbar {
    left: 200px;
  }
}

@media (max-width: 992px) {
  .financial-sider {
    transform: translateX(-100%);
  }
  
  .financial-sider.collapsed {
    transform: translateX(0);
  }
  
  .financial-main {
    margin-left: 0;
  }
  
  .financial-batch-toolbar {
    left: 0;
  }
  
  .financial-data-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .financial-toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .financial-toolbar-left,
  .financial-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .financial-workspace-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .financial-table-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .financial-table-toolbar-left,
  .financial-table-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .financial-batch-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .financial-batch-toolbar-left,
  .financial-batch-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
}

/* 新增：图标样式 */
.financial-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--yy-icon-size);
  height: var(--yy-icon-size);
  font-size: var(--yy-icon-size);
  line-height: 1;
  color: var(--yy-text-secondary);
  transition: all 0.3s;
}

.financial-icon-lg {
  width: var(--yy-icon-size-lg);
  height: var(--yy-icon-size-lg);
  font-size: var(--yy-icon-size-lg);
}

.financial-icon-sm {
  width: var(--yy-icon-size-sm);
  height: var(--yy-icon-size-sm);
  font-size: var(--yy-icon-size-sm);
}

.financial-icon-primary {
  color: var(--yy-primary);
}

.financial-icon-success {
  color: var(--yy-success);
}

.financial-icon-warning {
  color: var(--yy-warning);
}

.financial-icon-danger {
  color: var(--yy-danger);
}

/* 新增：链接样式 */
.financial-link {
  color: var(--yy-primary);
  text-decoration: none;
  transition: all 0.3s;
  cursor: pointer;
}

.financial-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-link:active {
  color: var(--yy-primary-active);
}

.financial-link.disabled {
  color: var(--yy-text-light);
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮样式优化 */
.financial-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
  border: none;
  gap: 8px;
}

.financial-btn i {
  font-size: var(--yy-icon-size);
}

.financial-btn-primary {
  background: var(--yy-primary);
  color: #fff;
}

.financial-btn-primary:hover {
  background: var(--yy-primary-hover);
}

.financial-btn-primary:active {
  background: var(--yy-primary-active);
}

.financial-btn-secondary {
  background: #fff;
  color: var(--yy-text);
  border: 1px solid var(--yy-border);
}

.financial-btn-secondary:hover {
  color: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-btn-success {
  background: var(--yy-success);
  color: #fff;
}

.financial-btn-success:hover {
  background: #73d13d;
}

.financial-btn-warning {
  background: var(--yy-warning);
  color: #fff;
}

.financial-btn-warning:hover {
  background: #ffc53d;
}

.financial-btn-danger {
  background: var(--yy-danger);
  color: #fff;
}

.financial-btn-danger:hover {
  background: #ff4d4f;
}

.financial-btn-text {
  background: transparent;
  color: var(--yy-text);
  padding: 4px 8px;
}

.financial-btn-text:hover {
  color: var(--yy-primary);
  background: rgba(24,144,255,0.1);
}

.financial-btn-link {
  background: transparent;
  color: var(--yy-primary);
  padding: 4px 8px;
}

.financial-btn-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-btn-sm {
  padding: 4px 12px;
  font-size: var(--yy-font-size-sm);
}

.financial-btn-lg {
  padding: 12px 20px;
  font-size: var(--yy-font-size-lg);
}

.financial-btn-block {
  width: 100%;
}

.financial-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮组样式 */
.financial-btn-group {
  display: inline-flex;
  border-radius: var(--yy-radius);
  overflow: hidden;
}

.financial-btn-group .financial-btn {
  border-radius: 0;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.financial-btn-group .financial-btn:first-child {
  border-top-left-radius: var(--yy-radius);
  border-bottom-left-radius: var(--yy-radius);
}

.financial-btn-group .financial-btn:last-child {
  border-top-right-radius: var(--yy-radius);
  border-bottom-right-radius: var(--yy-radius);
  border-right: none;
}

/* 新增：徽标样式 */
.financial-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: var(--yy-font-size-sm);
  line-height: 20px;
  border-radius: 10px;
  background: var(--yy-danger);
  color: #fff;
  font-weight: normal;
}

.financial-badge-dot {
  min-width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
}

.financial-badge-count {
  position: relative;
  display: inline-block;
}

.financial-badge-count .financial-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transform: translate(50%, -50%);
}

/* 新增：标签样式 */
.financial-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: var(--yy-font-size-sm);
  line-height: 1.5;
  border-radius: var(--yy-radius);
  background: var(--yy-table-header);
  color: var(--yy-text);
  margin-right: 8px;
  margin-bottom: 8px;
}

.financial-tag-primary {
  background: #e6f7ff;
  color: var(--yy-primary);
  border: 1px solid #91d5ff;
}

.financial-tag-success {
  background: #f6ffed;
  color: var(--yy-success);
  border: 1px solid #b7eb8f;
}

.financial-tag-warning {
  background: #fffbe6;
  color: var(--yy-warning);
  border: 1px solid #ffe58f;
}

.financial-tag-danger {
  background: #fff1f0;
  color: var(--yy-danger);
  border: 1px solid #ffa39e;
}

.financial-tag-close {
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-tag-close:hover {
  color: var(--yy-text);
}

/* 新增：分割线样式 */
.financial-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 16px 0;
  background: var(--yy-border-light);
}

.financial-divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  margin: 0 8px;
  vertical-align: middle;
  background: var(--yy-border-light);
}

.financial-divider-text {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: var(--yy-text-secondary);
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  white-space: nowrap;
}

.financial-divider-text::before,
.financial-divider-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--yy-border-light);
}

.financial-divider-text::before {
  margin-right: 16px;
}

.financial-divider-text::after {
  margin-left: 16px;
}

/* 新增：表单组件样式 */
.financial-form-item {
  margin-bottom: 24px;
}

.financial-form-item-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text);
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
}

.financial-form-item-label-required::before {
  content: '*';
  color: var(--yy-danger);
  margin-right: 4px;
}

.financial-form-item-help {
  margin-top: 4px;
  color: var(--yy-text-light);
  font-size: var(--yy-font-size-sm);
}

.financial-form-item-error {
  margin-top: 4px;
  color: var(--yy-danger);
  font-size: var(--yy-font-size-sm);
}

/* 新增：输入框样式 */
.financial-input {
  display: inline-flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-input-disabled {
  background: var(--yy-table-header);
  cursor: not-allowed;
  color: var(--yy-text-light);
}

.financial-input-error {
  border-color: var(--yy-danger);
}

.financial-input-error:focus {
  box-shadow: 0 0 0 2px rgba(245,34,45,0.2);
}

.financial-input-prefix,
.financial-input-suffix {
  display: flex;
  align-items: center;
  color: var(--yy-text-light);
}

.financial-input-prefix {
  margin-right: 8px;
}

.financial-input-suffix {
  margin-left: 8px;
}

/* 新增：文本域样式 */
.financial-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  resize: vertical;
}

.financial-textarea:hover {
  border-color: var(--yy-primary-hover);
}

.financial-textarea:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

/* 新增：选择器样式 */
.financial-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-select-selector {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
}

.financial-select-selector:hover {
  border-color: var(--yy-primary-hover);
}

.financial-select-selector:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  padding: 4px 0;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

.financial-select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.financial-select-option:hover {
  background: var(--yy-table-header);
}

.financial-select-option-selected {
  background: #e6f7ff;
  color: var(--yy-primary);
}

/* 新增：日期选择器样式 */
.financial-datepicker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-datepicker-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-datepicker-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-datepicker-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-datepicker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  padding: 12px;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

/* 新增：单选框样式 */
.financial-radio {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-radio-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-radio-input:checked + .financial-radio-inner {
  border-color: var(--yy-primary);
}

.financial-radio-input:checked + .financial-radio-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--yy-primary);
  border-radius: 50%;
}

/* 新增：复选框样式 */
.financial-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-checkbox-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-checkbox-input:checked + .financial-checkbox-inner {
  background: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-checkbox-input:checked + .financial-checkbox-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 4px;
  height: 8px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}

/* 新增：开关样式 */
.financial-switch {
  position: relative;
  display: inline-block;
  min-width: 44px;
  height: 22px;
  cursor: pointer;
}

.financial-switch-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-switch-inner {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  background: var(--yy-border);
  border-radius: 11px;
  transition: all 0.3s;
}

.financial-switch-inner::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-switch-input:checked + .financial-switch-inner {
  background: var(--yy-primary);
}

.financial-switch-input:checked + .financial-switch-inner::after {
  left: calc(100% - 20px);
}

/* 新增：上传组件样式 */
.financial-upload {
  display: inline-block;
  cursor: pointer;
}

.financial-upload-input {
  display: none;
}

.financial-upload-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  color: var(--yy-text);
  background: #fff;
  border: 1px dashed var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-upload-trigger:hover {
  border-color: var(--yy-primary);
  color: var(--yy-primary);
}

.financial-upload-list {
  margin-top: 8px;
}

.financial-upload-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  margin-bottom: 8px;
}

.financial-upload-item-name {
  flex: 1;
  margin: 0 8px;
  color: var(--yy-text);
}

.financial-upload-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.financial-upload-item-action {
  color: var(--yy-text-light);
  cursor: pointer;
  transition: all 0.3s;
}

.financial-upload-item-action:hover {
  color: var(--yy-primary);
}