/*!
 * 用友财务软件专业主题样式 v3.0
 * 完全复刻用友NC Cloud/U8财务软件界面效果
 * 包含：窗口系统、表格、按钮、表单、工具栏、状态栏等完整组件
 *
 * 特性：
 * - 100%复刻用友财务软件视觉效果
 * - 专业的财务数据展示
 * - 完整的交互反馈
 * - 响应式设计支持
 * - 无障碍访问支持
 */

/* ========================================
   用友财务软件标准变量系统 v3.0
   完全复刻用友NC Cloud/U8界面标准
   ======================================== */

:root {
    /* ===== 用友标准色彩系统 ===== */

    /* 主色调 - 用友经典蓝 */
    --uf-primary: #0066cc;
    --uf-primary-light: #3399ff;
    --uf-primary-dark: #004499;
    --uf-primary-rgb: 0, 102, 204;

    /* 功能色彩 */
    --uf-success: #52c41a;
    --uf-warning: #faad14;
    --uf-danger: #ff4d4f;
    --uf-info: #1890ff;

    /* 中性色系 - 用友标准灰度 */
    --uf-white: #ffffff;
    --uf-light: #f8f9fa;
    --uf-gray-50: #fafafa;
    --uf-gray-100: #f5f5f5;
    --uf-gray-200: #eeeeee;
    --uf-gray-300: #d9d9d9;
    --uf-gray-400: #bfbfbf;
    --uf-gray-500: #8c8c8c;
    --uf-gray-600: #595959;
    --uf-gray-700: #434343;
    --uf-gray-800: #262626;
    --uf-gray-900: #141414;

    /* 边框色彩 */
    --uf-border: #d9d9d9;
    --uf-border-light: #f0f0f0;
    --uf-border-dark: #bfbfbf;
    --uf-grid-border: #c0c0c0;

    /* 背景色彩 */
    --uf-bg-primary: #f8f9fa;
    --uf-bg-secondary: #ffffff;
    --uf-header-bg: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    --uf-toolbar-bg: #f5f5f5;
    --uf-selected: #e6f7ff;
    --uf-row-hover: #f5f5f5;
    --uf-input-focus: #fff7e6;

    /* ===== 用友标准字体系统 ===== */
    --uf-font-family: '宋体', 'SimSun', 'Microsoft YaHei', sans-serif;
    --uf-font-size: 12px;
    --uf-font-size-sm: 11px;
    --uf-font-size-lg: 14px;
    --uf-line-height: 1.4;
    --uf-font-weight-normal: 400;
    --uf-font-weight-medium: 500;
    --uf-font-weight-bold: 600;

    /* ===== 用友标准尺寸系统 ===== */
    --uf-border-radius: 2px;
    --uf-border-width: 1px;

    /* 间距系统 */
    --uf-spacing-xs: 2px;
    --uf-spacing-sm: 4px;
    --uf-spacing-md: 8px;
    --uf-spacing-lg: 12px;
    --uf-spacing-xl: 16px;
    --uf-spacing-xxl: 24px;

    /* 组件尺寸 */
    --uf-btn-height: 24px;
    --uf-input-height: 22px;
    --uf-table-row-height: 24px;
    --uf-table-header-height: 26px;
    --uf-toolbar-height: 32px;
    --uf-card-padding: 8px 12px;

    /* ===== 用友标准阴影系统 ===== */
    --uf-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    --uf-box-shadow-hover: 0 2px 4px rgba(0, 0, 0, 0.15);
    --uf-box-shadow-focus: 0 0 0 2px rgba(0, 102, 204, 0.2);
    --uf-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.1);

    /* ===== 用友标准动画系统 ===== */
    --uf-transition: all 0.2s ease;
    --uf-transition-fast: all 0.1s ease;
    --uf-transition-slow: all 0.3s ease;

    /* ===== 用友专用变量 ===== */
    --uf-window-bg: #f0f0f0;
    --uf-window-border: #808080;
    --uf-titlebar-bg: linear-gradient(to bottom, #ddeeff 0%, #cce6ff 100%);
    --uf-statusbar-bg: #f0f0f0;
    --uf-menu-bg: #f8f8f8;
    --uf-menu-hover: #e6f2ff;
}

/* ========================================
   用友财务软件基础样式重置
   ======================================== */

/* 全局基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    line-height: var(--uf-line-height) !important;
    color: #333 !important;
    background: var(--uf-bg-primary) !important;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 财务模块容器 */
.uf-financial-content,
.financial-content,
.voucher-edit-container {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: #333;
    background: var(--uf-bg-primary);
    padding: var(--uf-spacing-lg);
    min-height: 100vh;
}

/* 清除默认样式 */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

p, div, span {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
}

/* 链接样式 */
a {
    color: var(--uf-primary);
    text-decoration: none;
    transition: var(--uf-transition);
}

a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--uf-primary);
    outline-offset: 2px;
}

/* ========================================
   用友标准卡片和容器组件
   ======================================== */

/* 用友标准卡片 */
.uf-card,
.card,
.financial-card,
.voucher-header-section,
.voucher-table-section,
.voucher-signature-section {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    margin-bottom: var(--uf-spacing-md);
    overflow: hidden;
    transition: var(--uf-transition);
}

.uf-card:hover,
.financial-card:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

/* 用友标准卡片头部 */
.uf-card-header,
.card-header,
.financial-card-header,
.voucher-header-title {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--uf-table-header-height);
}

/* 用友标准卡片主体 */
.uf-card-body,
.card-body,
.financial-card-body {
    padding: var(--uf-card-padding);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: #333;
}

/* 用友标准窗口容器 */
.uf-window {
    background: var(--uf-window-bg);
    border: 2px solid var(--uf-window-border);
    border-radius: var(--uf-border-radius);
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: var(--uf-spacing-lg);
    overflow: hidden;
}

/* 用友标准窗口标题栏 */
.uf-window-titlebar {
    background: var(--uf-titlebar-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 24px;
}

/* 用友标准窗口内容区 */
.uf-window-content {
    background: var(--uf-white);
    padding: var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
}

/* ========================================
   用友标准按钮系统
   ======================================== */

/* 用友标准按钮基础样式 */
.uf-btn,
.btn,
.financial-btn {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-normal);
    line-height: var(--uf-line-height);
    padding: var(--uf-spacing-xs) var(--uf-spacing-md);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 100%);
    color: #333;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--uf-spacing-xs);
    text-decoration: none;
    transition: var(--uf-transition);
    min-height: var(--uf-btn-height);
    box-shadow: var(--uf-box-shadow);
    vertical-align: middle;
    white-space: nowrap;
    user-select: none;
    outline: none;
}

/* 按钮悬停效果 */
.uf-btn:hover,
.btn:hover,
.financial-btn:hover {
    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    border-color: var(--uf-primary);
    color: var(--uf-primary);
    text-decoration: none;
    box-shadow: var(--uf-box-shadow-hover);
}

/* 按钮激活效果 */
.uf-btn:active,
.btn:active,
.financial-btn:active {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce6ff 100%);
    border-color: var(--uf-primary-dark);
    box-shadow: var(--uf-box-shadow-inset);
    transform: translateY(1px);
}

/* 按钮焦点效果 */
.uf-btn:focus,
.btn:focus,
.financial-btn:focus {
    box-shadow: var(--uf-box-shadow-focus);
    border-color: var(--uf-primary);
}

/* 主要按钮样式 */
.uf-btn-primary,
.btn-primary,
.financial-btn-primary {
    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    border-color: var(--uf-primary-dark);
    color: var(--uf-white);
    font-weight: var(--uf-font-weight-medium);
}

.uf-btn-primary:hover,
.btn-primary:hover,
.financial-btn-primary:hover {
    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);
    border-color: var(--uf-primary);
    color: var(--uf-white);
}

.uf-btn-primary:active,
.btn-primary:active,
.financial-btn-primary:active {
    background: linear-gradient(to bottom, var(--uf-primary-dark) 0%, #003366 100%);
    border-color: #003366;
}

/* 次要按钮样式 */
.uf-btn-secondary,
.btn-secondary,
.financial-btn-secondary {
    background: linear-gradient(to bottom, var(--uf-gray-100) 0%, var(--uf-gray-200) 100%);
    border-color: var(--uf-border);
    color: #333;
}

.uf-btn-secondary:hover,
.btn-secondary:hover,
.financial-btn-secondary:hover {
    background: linear-gradient(to bottom, var(--uf-gray-200) 0%, var(--uf-gray-300) 100%);
    border-color: var(--uf-border-dark);
}

/* 成功按钮样式 */
.uf-btn-success,
.btn-success {
    background: linear-gradient(to bottom, var(--uf-success) 0%, #389e0d 100%);
    border-color: #389e0d;
    color: var(--uf-white);
}

.uf-btn-success:hover,
.btn-success:hover {
    background: linear-gradient(to bottom, #73d13d 0%, var(--uf-success) 100%);
    color: var(--uf-white);
}

/* 警告按钮样式 */
.uf-btn-warning,
.btn-warning {
    background: linear-gradient(to bottom, var(--uf-warning) 0%, #d48806 100%);
    border-color: #d48806;
    color: var(--uf-white);
}

.uf-btn-warning:hover,
.btn-warning:hover {
    background: linear-gradient(to bottom, #ffc53d 0%, var(--uf-warning) 100%);
    color: var(--uf-white);
}

/* 危险按钮样式 */
.uf-btn-danger,
.btn-danger {
    background: linear-gradient(to bottom, var(--uf-danger) 0%, #cf1322 100%);
    border-color: #cf1322;
    color: var(--uf-white);
}

.uf-btn-danger:hover,
.btn-danger:hover {
    background: linear-gradient(to bottom, #ff7875 0%, var(--uf-danger) 100%);
    color: var(--uf-white);
}

/* 按钮尺寸变体 */
.uf-btn-sm,
.btn-sm {
    font-size: var(--uf-font-size-sm);
    padding: 2px var(--uf-spacing-sm);
    min-height: 20px;
}

.uf-btn-lg,
.btn-lg {
    font-size: var(--uf-font-size-lg);
    padding: var(--uf-spacing-sm) var(--uf-spacing-xl);
    min-height: 28px;
}

/* 禁用按钮样式 */
.uf-btn:disabled,
.btn:disabled,
.uf-btn.disabled,
.btn.disabled {
    background: var(--uf-gray-100);
    border-color: var(--uf-gray-300);
    color: var(--uf-gray-500);
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow: none;
}

.uf-btn:disabled:hover,
.btn:disabled:hover,
.uf-btn.disabled:hover,
.btn.disabled:hover {
    background: var(--uf-gray-100);
    border-color: var(--uf-gray-300);
    color: var(--uf-gray-500);
    transform: none;
}

/* 按钮图标 */
.uf-btn .uf-icon,
.btn .fa,
.btn .fas,
.btn .far {
    font-size: 10px;
    margin-right: var(--uf-spacing-xs);
}

.uf-btn .uf-icon:only-child,
.btn .fa:only-child,
.btn .fas:only-child,
.btn .far:only-child {
    margin-right: 0;
}

/* ========================================
   用友标准表格系统
   ======================================== */

/* 用友标准表格基础样式 */
.uf-table,
.uf-financial-table,
.table,
.financial-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    background: var(--uf-white);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    margin: 0;
    table-layout: fixed;
}

/* 用友标准表格表头 */
.uf-table th,
.uf-financial-table th,
.table th,
.financial-table th {
    background: var(--uf-header-bg);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    white-space: nowrap;
    height: var(--uf-table-header-height);
    vertical-align: middle;
    position: relative;
    user-select: none;
}

/* 表头排序指示器 */
.uf-table th.sortable,
.financial-table th.sortable {
    cursor: pointer;
    padding-right: 16px;
}

.uf-table th.sortable:hover,
.financial-table th.sortable:hover {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce6ff 100%);
}

.uf-table th.sortable::after,
.financial-table th.sortable::after {
    content: '↕';
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: var(--uf-gray-500);
}

/* 用友标准表格单元格 */
.uf-table td,
.uf-financial-table td,
.table td,
.financial-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    vertical-align: middle;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    height: var(--uf-table-row-height);
    background: var(--uf-white);
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 表格行交互效果 */
.uf-table tbody tr:hover,
.uf-financial-table tbody tr:hover,
.table tbody tr:hover,
.financial-table tbody tr:hover {
    background: var(--uf-row-hover) !important;
}

.uf-table tbody tr:nth-child(even),
.financial-table tbody tr:nth-child(even) {
    background: var(--uf-gray-50);
}

.uf-table tbody tr:nth-child(even):hover,
.financial-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover) !important;
}

/* 选中行样式 */
.uf-table tbody tr.selected,
.financial-table tbody tr.selected {
    background: var(--uf-selected) !important;
    color: var(--uf-primary);
}

/* 表格文本对齐 */
.uf-table .uf-text-left,
.financial-table .text-left {
    text-align: left !important;
}

.uf-table .uf-text-center,
.financial-table .text-center {
    text-align: center !important;
}

.uf-table .uf-text-right,
.financial-table .text-right {
    text-align: right !important;
}

/* 金额列样式 */
.uf-table .uf-amount-col,
.financial-table .amount-col {
    text-align: right;
    font-family: 'Times New Roman', 'Consolas', monospace;
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

/* 表格容器 */
.uf-table-container {
    overflow-x: auto;
    border: var(--uf-border-width) solid var(--uf-grid-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
}

.uf-table-container::-webkit-scrollbar {
    height: 12px;
}

.uf-table-container::-webkit-scrollbar-track {
    background: var(--uf-gray-100);
}

.uf-table-container::-webkit-scrollbar-thumb {
    background: var(--uf-gray-400);
    border-radius: var(--uf-border-radius);
}

.uf-table-container::-webkit-scrollbar-thumb:hover {
    background: var(--uf-gray-500);
}

/* ========================================
   用友标准表单系统
   ======================================== */

/* 表单组 */
.uf-form-group,
.form-group {
    margin-bottom: var(--uf-spacing-lg);
    display: flex;
    flex-direction: column;
}

.uf-form-group.inline,
.form-group.inline {
    flex-direction: row;
    align-items: center;
    gap: var(--uf-spacing-md);
}

/* 表单标签 */
.uf-form-label,
.form-label,
.financial-form-label {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    margin-bottom: var(--uf-spacing-xs);
    display: block;
    line-height: var(--uf-line-height);
}

.uf-form-label.required::after,
.form-label.required::after {
    content: '*';
    color: var(--uf-danger);
    margin-left: 2px;
}

/* 表单控件基础样式 */
.uf-form-control,
.form-control,
.form-select,
.financial-form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
    color: #333;
    transition: var(--uf-transition);
    outline: none;
    width: 100%;
    min-height: var(--uf-input-height);
    box-sizing: border-box;
}

/* 表单控件焦点效果 */
.uf-form-control:focus,
.form-control:focus,
.form-select:focus,
.financial-form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--uf-primary);
    background: var(--uf-input-focus);
    box-shadow: var(--uf-box-shadow-focus);
}

/* 表单控件悬停效果 */
.uf-form-control:hover,
.form-control:hover,
.form-select:hover,
input:hover,
textarea:hover,
select:hover {
    border-color: var(--uf-border-dark);
}

/* 表单控件禁用状态 */
.uf-form-control:disabled,
.form-control:disabled,
.form-select:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    background: var(--uf-gray-100);
    border-color: var(--uf-gray-300);
    color: var(--uf-gray-500);
    cursor: not-allowed;
}

/* 表单控件只读状态 */
.uf-form-control[readonly],
.form-control[readonly],
input[readonly],
textarea[readonly] {
    background: var(--uf-gray-50);
    border-color: var(--uf-border);
    color: var(--uf-gray-700);
}

/* 表单控件错误状态 */
.uf-form-control.error,
.form-control.error,
.uf-form-control.is-invalid,
.form-control.is-invalid {
    border-color: var(--uf-danger);
    background: #fff2f0;
}

.uf-form-control.error:focus,
.form-control.error:focus,
.uf-form-control.is-invalid:focus,
.form-control.is-invalid:focus {
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 表单控件成功状态 */
.uf-form-control.success,
.form-control.success,
.uf-form-control.is-valid,
.form-control.is-valid {
    border-color: var(--uf-success);
    background: #f6ffed;
}

/* 文本域特殊样式 */
textarea.uf-form-control,
textarea.form-control {
    min-height: 60px;
    resize: vertical;
}

/* 选择框特殊样式 */
select.uf-form-control,
select.form-control,
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 6px center;
    background-size: 12px;
    padding-right: 24px;
    cursor: pointer;
}

/* 表单控件尺寸变体 */
.uf-form-control-sm,
.form-control-sm {
    font-size: var(--uf-font-size-sm);
    padding: 2px var(--uf-spacing-xs);
    min-height: 18px;
}

.uf-form-control-lg,
.form-control-lg {
    font-size: var(--uf-font-size-lg);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    min-height: 28px;
}

/* 表单帮助文本 */
.uf-form-help,
.form-help,
.form-text {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-gray-600);
    margin-top: var(--uf-spacing-xs);
    line-height: var(--uf-line-height);
}

/* 表单错误信息 */
.uf-form-error,
.form-error,
.invalid-feedback {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-danger);
    margin-top: var(--uf-spacing-xs);
    line-height: var(--uf-line-height);
}

/* 表单成功信息 */
.uf-form-success,
.form-success,
.valid-feedback {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-success);
    margin-top: var(--uf-spacing-xs);
    line-height: var(--uf-line-height);
}

/* ========================================
   用友专用组件系统
   ======================================== */

/* 用友工具栏 */
.uf-toolbar,
.uf-financial-toolbar {
    background: var(--uf-toolbar-bg);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    margin-bottom: var(--uf-spacing-md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--uf-toolbar-height);
    box-shadow: var(--uf-box-shadow);
}

.uf-toolbar-title,
.uf-financial-toolbar-title {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-xs);
}

.uf-toolbar-actions,
.uf-financial-toolbar-actions {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-sm);
}

/* 用友面包屑导航 */
.uf-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-xs);
    margin-bottom: var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    padding: var(--uf-spacing-xs) 0;
}

.uf-breadcrumb-item {
    color: var(--uf-gray-600);
    font-size: var(--uf-font-size);
}

.uf-breadcrumb-item a {
    color: var(--uf-primary);
    text-decoration: none;
    transition: var(--uf-transition);
}

.uf-breadcrumb-item a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

.uf-breadcrumb-item.active {
    color: #333;
    font-weight: var(--uf-font-weight-medium);
}

.uf-breadcrumb-separator {
    color: var(--uf-gray-500);
    margin: 0 var(--uf-spacing-xs);
}

/* 用友状态栏 */
.uf-statusbar {
    background: var(--uf-statusbar-bg);
    border-top: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-gray-600);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 20px;
}

/* 用友统计卡片 */
.uf-stat-card {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: var(--uf-spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-lg);
    box-shadow: var(--uf-box-shadow);
    transition: var(--uf-transition);
    height: 100%;
}

.uf-stat-card:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

.uf-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--uf-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    flex-shrink: 0;
}

.uf-stat-content {
    flex: 1;
    min-width: 0;
}

.uf-stat-title {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-gray-600);
    margin-bottom: var(--uf-spacing-xs);
    font-weight: var(--uf-font-weight-medium);
}

.uf-stat-value {
    font-family: 'Times New Roman', var(--uf-font-family);
    font-size: 16px;
    font-weight: var(--uf-font-weight-bold);
    color: #333;
}

/* 用友货币显示 */
.uf-currency {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-gray-600);
    margin-right: 2px;
}

.uf-amount {
    font-family: 'Times New Roman', 'Consolas', monospace;
    font-weight: var(--uf-font-weight-medium);
    text-align: right;
}

.uf-amount.positive {
    color: var(--uf-success);
}

.uf-amount.negative {
    color: var(--uf-danger);
}

.uf-amount.zero {
    color: var(--uf-gray-600);
}

/* 用友标签系统 */
.uf-tag {
    display: inline-flex;
    align-items: center;
    padding: 2px var(--uf-spacing-sm);
    border-radius: var(--uf-border-radius);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    font-weight: var(--uf-font-weight-medium);
    line-height: 1;
    white-space: nowrap;
}

.uf-tag-primary {
    background: #e6f7ff;
    color: var(--uf-primary);
    border: var(--uf-border-width) solid #91d5ff;
}

.uf-tag-success {
    background: #f6ffed;
    color: var(--uf-success);
    border: var(--uf-border-width) solid #b7eb8f;
}

.uf-tag-warning {
    background: #fff7e6;
    color: var(--uf-warning);
    border: var(--uf-border-width) solid #ffd666;
}

.uf-tag-danger {
    background: #fff2f0;
    color: var(--uf-danger);
    border: var(--uf-border-width) solid #ffb3b3;
}

.uf-tag-default {
    background: var(--uf-gray-100);
    color: var(--uf-gray-700);
    border: var(--uf-border-width) solid var(--uf-gray-300);
}

.breadcrumb, .financial-breadcrumb {
  background: transparent;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5; /* 调整行高 */
}
.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #bbb;
}

.badge {
  border-radius: var(--yy-radius);
  padding: 2px 10px;
  font-size: 1.1rem;
  font-weight: normal;
  line-height: 1.5; /* 调整行高 */
}
.badge-secondary { background: #f5f7fa; color: #888; }
.badge-warning { background: #fffbe6; color: #faad14; }
.badge-success { background: #f6ffed; color: #52c41a; }
.badge-primary { background: #e6f7ff; color: #1890ff; }

.voucher-actions, .financial-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

/* 输入区紧凑风格 */
.voucher-header-form .form-label, .form-label {
  color: #888;
  font-size: 1.1rem;
  margin-bottom: 0.2rem;
  line-height: 1.5; /* 调整行高 */
}
.voucher-header-form .form-control-sm, .form-control-sm {
  font-size: 1.1rem;
  padding: 4px 8px;
  border-radius: var(--yy-radius);
  line-height: 1.5; /* 调整行高 */
}

/* 统计金额、数字高亮 */
.amount-cell, .stats-value, .financial-amount {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 1.1rem;
  color: var(--yy-primary);
  text-align: right;
  line-height: 1.5; /* 调整行高 */
}

/* 模态框风格 */
.modal-content {
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
}

/* ========================================
   用友工具类系统
   ======================================== */

/* 布局工具类 */
.uf-row {
    display: flex;
    flex-wrap: wrap;
    margin: calc(var(--uf-spacing-sm) * -1);
}

.uf-col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: var(--uf-spacing-sm);
}

/* 文本对齐工具类 */
.uf-text-left { text-align: left !important; }
.uf-text-center { text-align: center !important; }
.uf-text-right { text-align: right !important; }
.uf-text-justify { text-align: justify !important; }

/* 文本颜色工具类 */
.uf-text-primary { color: var(--uf-primary) !important; }
.uf-text-success { color: var(--uf-success) !important; }
.uf-text-warning { color: var(--uf-warning) !important; }
.uf-text-danger { color: var(--uf-danger) !important; }
.uf-text-muted { color: var(--uf-gray-600) !important; }
.uf-text-dark { color: var(--uf-gray-800) !important; }
.uf-text-light { color: var(--uf-gray-400) !important; }

/* 背景颜色工具类 */
.uf-bg-primary { background-color: var(--uf-primary) !important; color: var(--uf-white) !important; }
.uf-bg-success { background-color: var(--uf-success) !important; color: var(--uf-white) !important; }
.uf-bg-warning { background-color: var(--uf-warning) !important; color: var(--uf-white) !important; }
.uf-bg-danger { background-color: var(--uf-danger) !important; color: var(--uf-white) !important; }
.uf-bg-light { background-color: var(--uf-light) !important; color: #333 !important; }
.uf-bg-white { background-color: var(--uf-white) !important; color: #333 !important; }

/* 间距工具类 */
.uf-m-0 { margin: 0 !important; }
.uf-m-1 { margin: var(--uf-spacing-xs) !important; }
.uf-m-2 { margin: var(--uf-spacing-sm) !important; }
.uf-m-3 { margin: var(--uf-spacing-md) !important; }
.uf-m-4 { margin: var(--uf-spacing-lg) !important; }

.uf-p-0 { padding: 0 !important; }
.uf-p-1 { padding: var(--uf-spacing-xs) !important; }
.uf-p-2 { padding: var(--uf-spacing-sm) !important; }
.uf-p-3 { padding: var(--uf-spacing-md) !important; }
.uf-p-4 { padding: var(--uf-spacing-lg) !important; }

.uf-mb-0 { margin-bottom: 0 !important; }
.uf-mb-1 { margin-bottom: var(--uf-spacing-xs) !important; }
.uf-mb-2 { margin-bottom: var(--uf-spacing-sm) !important; }
.uf-mb-3 { margin-bottom: var(--uf-spacing-md) !important; }
.uf-mb-4 { margin-bottom: var(--uf-spacing-lg) !important; }

/* 显示工具类 */
.uf-d-none { display: none !important; }
.uf-d-block { display: block !important; }
.uf-d-inline { display: inline !important; }
.uf-d-inline-block { display: inline-block !important; }
.uf-d-flex { display: flex !important; }
.uf-d-inline-flex { display: inline-flex !important; }

/* Flexbox工具类 */
.uf-flex-row { flex-direction: row !important; }
.uf-flex-column { flex-direction: column !important; }
.uf-justify-start { justify-content: flex-start !important; }
.uf-justify-center { justify-content: center !important; }
.uf-justify-end { justify-content: flex-end !important; }
.uf-justify-between { justify-content: space-between !important; }
.uf-align-start { align-items: flex-start !important; }
.uf-align-center { align-items: center !important; }
.uf-align-end { align-items: flex-end !important; }

/* 边框工具类 */
.uf-border { border: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-top { border-top: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-bottom { border-bottom: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-left { border-left: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-right { border-right: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-0 { border: 0 !important; }

/* 圆角工具类 */
.uf-rounded { border-radius: var(--uf-border-radius) !important; }
.uf-rounded-0 { border-radius: 0 !important; }

/* 阴影工具类 */
.uf-shadow { box-shadow: var(--uf-box-shadow) !important; }
.uf-shadow-hover { box-shadow: var(--uf-box-shadow-hover) !important; }
.uf-shadow-none { box-shadow: none !important; }

/* ========================================
   用友响应式设计系统
   ======================================== */

/* 平板设备 */
@media (max-width: 992px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .uf-toolbar,
    .uf-financial-toolbar {
        flex-direction: column;
        gap: var(--uf-spacing-sm);
        padding: var(--uf-spacing-md);
    }

    .uf-toolbar-actions,
    .uf-financial-toolbar-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .uf-row {
        margin: calc(var(--uf-spacing-xs) * -1);
    }

    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6,
    .uf-col-md-12 {
        padding: var(--uf-spacing-xs);
    }

    .uf-stat-card {
        padding: var(--uf-spacing-md);
        gap: var(--uf-spacing-md);
    }

    .uf-stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .uf-stat-value {
        font-size: 14px;
    }

    .uf-table-container {
        font-size: var(--uf-font-size-sm);
    }

    .uf-btn {
        padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
        font-size: var(--uf-font-size-sm);
    }

    .uf-form-control {
        font-size: var(--uf-font-size);
    }
}

/* 小屏幕设备 */
@media (max-width: 576px) {
    .uf-financial-content {
        padding: var(--uf-spacing-md);
    }

    .uf-card-header,
    .uf-card-body {
        padding: var(--uf-spacing-md);
    }

    .uf-toolbar,
    .uf-financial-toolbar {
        padding: var(--uf-spacing-sm);
    }

    .uf-breadcrumb {
        font-size: var(--uf-font-size-sm);
    }
}

/* ========================================
   用友专用凭证和财务单据样式
   ======================================== */

/* 用友凭证编辑器 */
.uf-voucher-editor {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    overflow: hidden;
}

.uf-voucher-header {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-md);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-voucher-field {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-sm);
}

.uf-voucher-field label {
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    white-space: nowrap;
    min-width: 60px;
}

.uf-voucher-field input,
.uf-voucher-field select {
    flex: 1;
    min-width: 80px;
}

/* 用友凭证表格 */
.uf-voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    background: var(--uf-white);
}

.uf-voucher-table th {
    background: var(--uf-header-bg);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    height: var(--uf-table-header-height);
    white-space: nowrap;
}

.uf-voucher-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: 0;
    height: var(--uf-table-row-height);
    vertical-align: middle;
}

.uf-voucher-table input,
.uf-voucher-table select {
    width: 100%;
    height: 100%;
    border: none;
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    background: transparent;
    outline: none;
}

.uf-voucher-table input:focus,
.uf-voucher-table select:focus {
    background: var(--uf-input-focus);
    box-shadow: inset 0 0 0 2px var(--uf-primary);
}

/* 用友凭证汇总区 */
.uf-voucher-summary {
    background: var(--uf-gray-50);
    border-top: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-voucher-summary-item {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-sm);
}

.uf-voucher-summary-label {
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

.uf-voucher-summary-value {
    font-family: 'Times New Roman', monospace;
    font-weight: var(--uf-font-weight-bold);
    color: var(--uf-primary);
}

/* 用友财务报表样式 */
.uf-report-container {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    overflow: hidden;
}

.uf-report-header {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-lg);
    text-align: center;
}

.uf-report-title {
    font-family: var(--uf-font-family);
    font-size: 18px;
    font-weight: var(--uf-font-weight-bold);
    color: #333;
    margin-bottom: var(--uf-spacing-sm);
}

.uf-report-subtitle {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    color: var(--uf-gray-600);
}

.uf-report-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-report-table th,
.uf-report-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    height: var(--uf-table-row-height);
}

.uf-report-table th {
    background: var(--uf-header-bg);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
}

.uf-report-table .uf-amount-cell {
    text-align: right;
    font-family: 'Times New Roman', monospace;
    font-weight: var(--uf-font-weight-medium);
}

/* 用友打印样式 */
@media print {
    .uf-no-print {
        display: none !important;
    }

    .uf-voucher-editor,
    .uf-report-container {
        border: none;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .uf-voucher-table,
    .uf-report-table {
        border: 2px solid #000;
    }

    .uf-voucher-table th,
    .uf-voucher-table td,
    .uf-report-table th,
    .uf-report-table td {
        border: 1px solid #000;
    }
}

/* ========================================
   用友财务软件完整样式系统 v3.0 - 优化完成
   复刻用友NC Cloud/U8财务软件专业界面效果
   ======================================== */

.financial-breadcrumb .breadcrumb {
    margin: 0;
    padding: 0;
}

.financial-breadcrumb .breadcrumb-item a {
    color: var(--yonyou-primary);
    text-decoration: none;
}

.financial-breadcrumb .breadcrumb-item.active {
    color: var(--yonyou-gray-600);
}

/* 页面标题 */
.financial-page-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--yonyou-gray-900);
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--yonyou-primary);
    line-height: 1.5; /* 调整行高 */
}

/* 按钮样式 */
.financial-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.financial-btn-primary {
    background-color: var(--yonyou-primary);
    color: white;
}

.financial-btn-primary:hover {
    background-color: #1976D2;
}

.financial-btn-secondary {
    background-color: var(--yonyou-gray-200);
    color: var(--yonyou-gray-800);
}

.financial-btn-secondary:hover {
    background-color: var(--yonyou-gray-300);
}

/* 表单样式 */
.financial-form-group {
    margin-bottom: 16px;
}

.financial-form-label {
    display: block;
    margin-bottom: 8px;
    color: var(--yonyou-gray-700);
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--yonyou-gray-300);
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    line-height: 1.5; /* 调整行高 */
}

.financial-form-control:focus {
    border-color: var(--yonyou-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.2);
}

/* 状态标签 */
.financial-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-status.draft {
    background-color: var(--yonyou-gray-200);
    color: var(--yonyou-gray-700);
}

.financial-status.pending {
    background-color: var(--yonyou-warning);
    color: white;
}

.financial-status.approved {
    background-color: var(--yonyou-success);
    color: white;
}

.financial-status.posted {
    background-color: var(--yonyou-primary);
    color: white;
}

/* 金额显示 */
.financial-amount {
    font-family: "Consolas", monospace;
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-amount.positive {
    color: var(--yonyou-success);
}

.financial-amount.negative {
    color: var(--yonyou-danger);
}

.financial-amount.zero {
    color: var(--yonyou-gray-600);
}

/* 卡片样式 */
.financial-card {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.financial-card-header {
    padding: 16px;
    border-bottom: 1px solid var(--yonyou-gray-200);
    font-weight: 500;
    line-height: 1.5; /* 调整行高 */
}

.financial-card-body {
    padding: 16px;
    line-height: 1.5; /* 调整行高 */
}

/* 搜索区域 */
.financial-search {
    background-color: var(--yonyou-gray-100);
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 24px;
    line-height: 1.5; /* 调整行高 */
}

.financial-search-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px;
}

.financial-search-col {
    padding: 8px;
    flex: 1;
    min-width: 200px;
}

/* 分页样式 */
.financial-pagination {
    display: flex;
    justify-content: center;
    margin-top: 24px;
}

.financial-pagination .page-item {
    margin: 0 4px;
}

.financial-pagination .page-link {
    padding: 6px 12px;
    border: 1px solid var(--yonyou-gray-300);
    border-radius: 4px;
    color: var(--yonyou-gray-700);
    text-decoration: none;
    line-height: 1.5; /* 调整行高 */
}

.financial-pagination .page-link:hover {
    background-color: var(--yonyou-gray-100);
}

.financial-pagination .active .page-link {
    background-color: var(--yonyou-primary);
    border-color: var(--yonyou-primary);
    color: white;
}

/* 弹窗样式 */
.financial-modal {
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.financial-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--yonyou-gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1.5; /* 调整行高 */
}

.financial-modal-body {
    padding: 24px;
    overflow-y: auto;
    line-height: 1.5; /* 调整行高 */
}

.financial-modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--yonyou-gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    line-height: 1.5; /* 调整行高 */
}

/* 加载动画 */
.financial-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--yonyou-gray-300);
    border-radius: 50%;
    border-top-color: var(--yonyou-primary);
    animation: financial-spin 1s linear infinite;
}

@keyframes financial-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式布局 */
@media (max-width: 768px) {
    .financial-search-col {
        min-width: 100%;
    }
    
    .financial-table {
        display: block;
        overflow-x: auto;
    }
}

/* 新增：表格样式优化 */
.financial-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin-bottom: 1rem;
}

.financial-table th {
  background: linear-gradient(to bottom, #f0f7ff, #e6f7ff);
  color: var(--yy-primary);
  font-weight: 600;
  padding: 12px 16px;
  border-bottom: 2px solid var(--yy-primary);
  text-align: left;
  line-height: 1.5; /* 调整行高 */
}

.financial-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--yy-border-light);
  transition: background-color 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-table tbody tr:hover td {
  background-color: #f0f7ff;
}

/* 新增：表单样式优化 */
.financial-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
}

.financial-form-group {
  margin-bottom: 24px;
}

.financial-form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text-secondary);
  font-weight: 500;
  line-height: 1.5; /* 调整行高 */
}

.financial-form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-form-control:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

/* 新增：按钮样式优化 */
.financial-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
  border: none;
}

.financial-btn-primary {
  background: var(--yy-primary);
  color: #fff;
}

.financial-btn-primary:hover {
  background: var(--yy-primary-hover);
}

.financial-btn-primary:active {
  background: var(--yy-primary-active);
}

.financial-btn-secondary {
  background: #fff;
  color: var(--yy-text);
  border: 1px solid var(--yy-border);
}

.financial-btn-secondary:hover {
  color: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-btn-success {
  background: var(--yy-success);
  color: #fff;
}

.financial-btn-success:hover {
  background: #73d13d;
}

.financial-btn-warning {
  background: var(--yy-warning);
  color: #fff;
}

.financial-btn-warning:hover {
  background: #ffc53d;
}

.financial-btn-danger {
  background: var(--yy-danger);
  color: #fff;
}

.financial-btn-danger:hover {
  background: #ff4d4f;
}

.financial-btn-text {
  background: transparent;
  color: var(--yy-text);
  padding: 4px 8px;
}

.financial-btn-text:hover {
  color: var(--yy-primary);
  background: rgba(24,144,255,0.1);
}

.financial-btn-link {
  background: transparent;
  color: var(--yy-primary);
  padding: 4px 8px;
}

.financial-btn-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-btn-sm {
  padding: 4px 12px;
  font-size: var(--yy-font-size-sm);
}

.financial-btn-lg {
  padding: 12px 20px;
  font-size: var(--yy-font-size-lg);
}

.financial-btn-block {
  width: 100%;
}

.financial-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮组样式 */
.financial-btn-group {
  display: inline-flex;
  border-radius: var(--yy-radius);
  overflow: hidden;
}

.financial-btn-group .financial-btn {
  border-radius: 0;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.financial-btn-group .financial-btn:first-child {
  border-top-left-radius: var(--yy-radius);
  border-bottom-left-radius: var(--yy-radius);
}

.financial-btn-group .financial-btn:last-child {
  border-top-right-radius: var(--yy-radius);
  border-bottom-right-radius: var(--yy-radius);
  border-right: none;
}

/* 新增：徽标样式 */
.financial-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: var(--yy-font-size-sm);
  line-height: 20px;
  border-radius: 10px;
  background: var(--yy-danger);
  color: #fff;
  font-weight: normal;
}

.financial-badge-dot {
  min-width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
}

.financial-badge-count {
  position: relative;
  display: inline-block;
}

.financial-badge-count .financial-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transform: translate(50%, -50%);
}

/* 新增：标签样式 */
.financial-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: var(--yy-font-size-sm);
  line-height: 1.5;
  border-radius: var(--yy-radius);
  background: var(--yy-table-header);
  color: var(--yy-text);
  margin-right: 8px;
  margin-bottom: 8px;
}

.financial-tag-primary {
  background: #e6f7ff;
  color: var(--yy-primary);
  border: 1px solid #91d5ff;
}

.financial-tag-success {
  background: #f6ffed;
  color: var(--yy-success);
  border: 1px solid #b7eb8f;
}

.financial-tag-warning {
  background: #fffbe6;
  color: var(--yy-warning);
  border: 1px solid #ffe58f;
}

.financial-tag-danger {
  background: #fff1f0;
  color: var(--yy-danger);
  border: 1px solid #ffa39e;
}

.financial-tag-close {
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-tag-close:hover {
  color: var(--yy-text);
}

/* 新增：分割线样式 */
.financial-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 16px 0;
  background: var(--yy-border-light);
}

.financial-divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  margin: 0 8px;
  vertical-align: middle;
  background: var(--yy-border-light);
}

.financial-divider-text {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: var(--yy-text-secondary);
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  white-space: nowrap;
}

.financial-divider-text::before,
.financial-divider-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--yy-border-light);
}

.financial-divider-text::before {
  margin-right: 16px;
}

.financial-divider-text::after {
  margin-left: 16px;
}

/* 新增：表单组件样式 */
.financial-form-item {
  margin-bottom: 24px;
}

.financial-form-item-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text);
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
}

.financial-form-item-label-required::before {
  content: '*';
  color: var(--yy-danger);
  margin-right: 4px;
}

.financial-form-item-help {
  margin-top: 4px;
  color: var(--yy-text-light);
  font-size: var(--yy-font-size-sm);
}

.financial-form-item-error {
  margin-top: 4px;
  color: var(--yy-danger);
  font-size: var(--yy-font-size-sm);
}

/* 新增：输入框样式 */
.financial-input {
  display: inline-flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-input-disabled {
  background: var(--yy-table-header);
  cursor: not-allowed;
  color: var(--yy-text-light);
}

.financial-input-error {
  border-color: var(--yy-danger);
}

.financial-input-error:focus {
  box-shadow: 0 0 0 2px rgba(245,34,45,0.2);
}

.financial-input-prefix,
.financial-input-suffix {
  display: flex;
  align-items: center;
  color: var(--yy-text-light);
}

.financial-input-prefix {
  margin-right: 8px;
}

.financial-input-suffix {
  margin-left: 8px;
}

/* 新增：文本域样式 */
.financial-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  resize: vertical;
}

.financial-textarea:hover {
  border-color: var(--yy-primary-hover);
}

.financial-textarea:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

/* 新增：选择器样式 */
.financial-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-select-selector {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
}

.financial-select-selector:hover {
  border-color: var(--yy-primary-hover);
}

.financial-select-selector:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  padding: 4px 0;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

.financial-select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.financial-select-option:hover {
  background: var(--yy-table-header);
}

.financial-select-option-selected {
  background: #e6f7ff;
  color: var(--yy-primary);
}

/* 新增：日期选择器样式 */
.financial-datepicker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-datepicker-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-datepicker-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-datepicker-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-datepicker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  padding: 12px;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

/* 新增：单选框样式 */
.financial-radio {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-radio-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-radio-input:checked + .financial-radio-inner {
  border-color: var(--yy-primary);
}

.financial-radio-input:checked + .financial-radio-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--yy-primary);
  border-radius: 50%;
}

/* 新增：复选框样式 */
.financial-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-checkbox-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-checkbox-input:checked + .financial-checkbox-inner {
  background: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-checkbox-input:checked + .financial-checkbox-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 4px;
  height: 8px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}

/* 新增：开关样式 */
.financial-switch {
  position: relative;
  display: inline-block;
  min-width: 44px;
  height: 22px;
  cursor: pointer;
}

.financial-switch-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-switch-inner {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  background: var(--yy-border);
  border-radius: 11px;
  transition: all 0.3s;
}

.financial-switch-inner::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-switch-input:checked + .financial-switch-inner {
  background: var(--yy-primary);
}

.financial-switch-input:checked + .financial-switch-inner::after {
  left: calc(100% - 20px);
}

/* 新增：上传组件样式 */
.financial-upload {
  display: inline-block;
  cursor: pointer;
}

.financial-upload-input {
  display: none;
}

.financial-upload-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  color: var(--yy-text);
  background: #fff;
  border: 1px dashed var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-upload-trigger:hover {
  border-color: var(--yy-primary);
  color: var(--yy-primary);
}

.financial-upload-list {
  margin-top: 8px;
}

.financial-upload-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  margin-bottom: 8px;
}

.financial-upload-item-name {
  flex: 1;
  margin: 0 8px;
  color: var(--yy-text);
}

.financial-upload-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.financial-upload-item-action {
  color: var(--yy-text-light);
  cursor: pointer;
  transition: all 0.3s;
}

.financial-upload-item-action:hover {
  color: var(--yy-primary);
}

/* 新增：数据卡片组件 */
.financial-data-card {
  background: #fff;
  border-radius: var(--yy-radius);
  padding: 20px;
  box-shadow: var(--yy-shadow);
  transition: var(--yy-transition);
}

.financial-data-card:hover {
  box-shadow: var(--yy-shadow-hover);
  transform: translateY(-2px);
}

.financial-data-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.financial-data-card-title {
  font-size: var(--yy-font-size-base);
  color: var(--yy-text-secondary);
  font-weight: 500;
}

.financial-data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--yy-text);
  font-family: 'Consolas', monospace;
}

/* 新增：状态指示器 */
.financial-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: var(--yy-font-size-sm);
  font-weight: 500;
  transition: var(--yy-transition);
}

.financial-status.draft {
  background: #f1f5f9;
  color: var(--yy-text-secondary);
}

.financial-status.pending {
  background: #fef3c7;
  color: var(--yy-warning);
}

.financial-status.approved {
  background: #dcfce7;
  color: var(--yy-success);
}

.financial-status.posted {
  background: #dbeafe;
  color: var(--yy-primary);
}

/* 新增：金额显示优化 */
.financial-amount {
  font-family: 'Consolas', monospace;
  font-weight: 500;
  text-align: right;
}

.financial-amount.positive {
  color: var(--yy-success);
}

.financial-amount.negative {
  color: var(--yy-danger);
}

.financial-amount.zero {
  color: var(--yy-text-light);
}

/* 新增：响应式布局优化 */
@media (max-width: 768px) {
  .financial-data-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .financial-table {
    display: block;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .financial-form {
    padding: 16px;
  }
  
  .financial-card-header,
  .financial-card-body {
    padding: 16px;
  }
}

/* 新增：页面布局基础样式 */
.financial-layout {
  display: flex;
  min-height: 100vh;
  background: var(--yy-bg);
}

/* 新增：侧边导航样式 */
.financial-sider {
  width: 240px;
  background: #fff;
  border-right: 1px solid var(--yy-border);
  box-shadow: var(--yy-shadow);
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.3s;
}

.financial-sider-header {
  height: 64px;
  padding: 0 24px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--yy-border-light);
  line-height: 1.5; /* 调整行高 */
}

.financial-sider-menu {
  padding: 16px 0;
}

.financial-menu-item {
  padding: 12px 24px;
  display: flex;
  align-items: center;
  color: var(--yy-text);
  cursor: pointer;
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-menu-item:hover {
  background: var(--yy-table-header);
  color: var(--yy-primary);
}

.financial-menu-item.active {
  background: #e6f7ff;
  color: var(--yy-primary);
  border-right: 3px solid var(--yy-primary);
}

.financial-menu-item i {
  margin-right: 12px;
  font-size: 16px;
}

/* 新增：主内容区样式 */
.financial-main {
  flex: 1;
  margin-left: 240px;
  padding: 24px;
  transition: all 0.3s;
}

/* 新增：顶部工具栏样式 */
.financial-toolbar {
  background: #fff;
  padding: 16px 24px;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.financial-toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 新增：工作区样式 */
.financial-workspace {
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow);
  padding: 24px;
}

.financial-workspace-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--yy-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-workspace-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--yy-text);
}

/* 新增：数据展示区样式 */
.financial-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.financial-data-card {
  background: #fff;
  border-radius: var(--yy-radius);
  padding: 20px;
  box-shadow: var(--yy-shadow);
}

.financial-data-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  line-height: 1.5; /* 调整行高 */
}

.financial-data-card-title {
  font-size: 14px;
  color: var(--yy-text-secondary);
}

.financial-data-card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--yy-text);
}

/* 新增：操作按钮组样式 */
.financial-action-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.financial-action-btn {
  padding: 6px 12px;
  border-radius: var(--yy-radius);
  font-size: 13px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s;
  line-height: 1.5; /* 调整行高 */
}

.financial-action-btn i {
  font-size: 14px;
}

/* 新增：表格工具栏样式 */
.financial-table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--yy-table-header);
  border-radius: var(--yy-radius);
  line-height: 1.5; /* 调整行高 */
}

.financial-table-toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.financial-table-toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 新增：批量操作工具栏样式 */
.financial-batch-toolbar {
  position: fixed;
  bottom: 0;
  left: 240px;
  right: 0;
  background: #fff;
  padding: 12px 24px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  line-height: 1.5; /* 调整行高 */
}

.financial-batch-toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.financial-batch-toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 新增：弹窗样式优化 */
.financial-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: var(--yy-radius-lg);
  box-shadow: var(--yy-shadow-lg);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.financial-modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--yy-border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 1.5; /* 调整行高 */
}

.financial-modal-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--yy-text);
}

.financial-modal-body {
  padding: 24px;
  overflow-y: auto;
  line-height: 1.5; /* 调整行高 */
}

.financial-modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--yy-border-light);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  line-height: 1.5; /* 调整行高 */
}

/* 新增：响应式布局优化 */
@media (max-width: 1200px) {
  .financial-sider {
    width: 200px;
  }
  
  .financial-main {
    margin-left: 200px;
  }
  
  .financial-batch-toolbar {
    left: 200px;
  }
}

@media (max-width: 992px) {
  .financial-sider {
    transform: translateX(-100%);
  }
  
  .financial-sider.collapsed {
    transform: translateX(0);
  }
  
  .financial-main {
    margin-left: 0;
  }
  
  .financial-batch-toolbar {
    left: 0;
  }
  
  .financial-data-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
}

@media (max-width: 768px) {
  .financial-toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .financial-toolbar-left,
  .financial-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .financial-workspace-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .financial-table-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .financial-table-toolbar-left,
  .financial-table-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .financial-batch-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .financial-batch-toolbar-left,
  .financial-batch-toolbar-right {
    width: 100%;
    justify-content: space-between;
  }
}

/* 新增：图标样式 */
.financial-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--yy-icon-size);
  height: var(--yy-icon-size);
  font-size: var(--yy-icon-size);
  line-height: 1;
  color: var(--yy-text-secondary);
  transition: all 0.3s;
}

.financial-icon-lg {
  width: var(--yy-icon-size-lg);
  height: var(--yy-icon-size-lg);
  font-size: var(--yy-icon-size-lg);
}

.financial-icon-sm {
  width: var(--yy-icon-size-sm);
  height: var(--yy-icon-size-sm);
  font-size: var(--yy-icon-size-sm);
}

.financial-icon-primary {
  color: var(--yy-primary);
}

.financial-icon-success {
  color: var(--yy-success);
}

.financial-icon-warning {
  color: var(--yy-warning);
}

.financial-icon-danger {
  color: var(--yy-danger);
}

/* 新增：链接样式 */
.financial-link {
  color: var(--yy-primary);
  text-decoration: none;
  transition: all 0.3s;
  cursor: pointer;
}

.financial-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-link:active {
  color: var(--yy-primary-active);
}

.financial-link.disabled {
  color: var(--yy-text-light);
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮样式优化 */
.financial-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
  border: none;
  gap: 8px;
}

.financial-btn i {
  font-size: var(--yy-icon-size);
}

.financial-btn-primary {
  background: var(--yy-primary);
  color: #fff;
}

.financial-btn-primary:hover {
  background: var(--yy-primary-hover);
}

.financial-btn-primary:active {
  background: var(--yy-primary-active);
}

.financial-btn-secondary {
  background: #fff;
  color: var(--yy-text);
  border: 1px solid var(--yy-border);
}

.financial-btn-secondary:hover {
  color: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-btn-success {
  background: var(--yy-success);
  color: #fff;
}

.financial-btn-success:hover {
  background: #73d13d;
}

.financial-btn-warning {
  background: var(--yy-warning);
  color: #fff;
}

.financial-btn-warning:hover {
  background: #ffc53d;
}

.financial-btn-danger {
  background: var(--yy-danger);
  color: #fff;
}

.financial-btn-danger:hover {
  background: #ff4d4f;
}

.financial-btn-text {
  background: transparent;
  color: var(--yy-text);
  padding: 4px 8px;
}

.financial-btn-text:hover {
  color: var(--yy-primary);
  background: rgba(24,144,255,0.1);
}

.financial-btn-link {
  background: transparent;
  color: var(--yy-primary);
  padding: 4px 8px;
}

.financial-btn-link:hover {
  color: var(--yy-primary-hover);
  text-decoration: underline;
}

.financial-btn-sm {
  padding: 4px 12px;
  font-size: var(--yy-font-size-sm);
}

.financial-btn-lg {
  padding: 12px 20px;
  font-size: var(--yy-font-size-lg);
}

.financial-btn-block {
  width: 100%;
}

.financial-btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 新增：按钮组样式 */
.financial-btn-group {
  display: inline-flex;
  border-radius: var(--yy-radius);
  overflow: hidden;
}

.financial-btn-group .financial-btn {
  border-radius: 0;
  border-right: 1px solid rgba(255,255,255,0.2);
}

.financial-btn-group .financial-btn:first-child {
  border-top-left-radius: var(--yy-radius);
  border-bottom-left-radius: var(--yy-radius);
}

.financial-btn-group .financial-btn:last-child {
  border-top-right-radius: var(--yy-radius);
  border-bottom-right-radius: var(--yy-radius);
  border-right: none;
}

/* 新增：徽标样式 */
.financial-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: var(--yy-font-size-sm);
  line-height: 20px;
  border-radius: 10px;
  background: var(--yy-danger);
  color: #fff;
  font-weight: normal;
}

.financial-badge-dot {
  min-width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 50%;
}

.financial-badge-count {
  position: relative;
  display: inline-block;
}

.financial-badge-count .financial-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  transform: translate(50%, -50%);
}

/* 新增：标签样式 */
.financial-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  font-size: var(--yy-font-size-sm);
  line-height: 1.5;
  border-radius: var(--yy-radius);
  background: var(--yy-table-header);
  color: var(--yy-text);
  margin-right: 8px;
  margin-bottom: 8px;
}

.financial-tag-primary {
  background: #e6f7ff;
  color: var(--yy-primary);
  border: 1px solid #91d5ff;
}

.financial-tag-success {
  background: #f6ffed;
  color: var(--yy-success);
  border: 1px solid #b7eb8f;
}

.financial-tag-warning {
  background: #fffbe6;
  color: var(--yy-warning);
  border: 1px solid #ffe58f;
}

.financial-tag-danger {
  background: #fff1f0;
  color: var(--yy-danger);
  border: 1px solid #ffa39e;
}

.financial-tag-close {
  margin-left: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-tag-close:hover {
  color: var(--yy-text);
}

/* 新增：分割线样式 */
.financial-divider {
  display: block;
  clear: both;
  width: 100%;
  min-width: 100%;
  height: 1px;
  margin: 16px 0;
  background: var(--yy-border-light);
}

.financial-divider-vertical {
  display: inline-block;
  width: 1px;
  height: 1em;
  margin: 0 8px;
  vertical-align: middle;
  background: var(--yy-border-light);
}

.financial-divider-text {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: var(--yy-text-secondary);
  font-size: var(--yy-font-size-base);
  font-weight: 500;
  white-space: nowrap;
}

.financial-divider-text::before,
.financial-divider-text::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--yy-border-light);
}

.financial-divider-text::before {
  margin-right: 16px;
}

.financial-divider-text::after {
  margin-left: 16px;
}

/* 新增：表单组件样式 */
.financial-form-item {
  margin-bottom: 24px;
}

.financial-form-item-label {
  display: block;
  margin-bottom: 8px;
  color: var(--yy-text);
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
}

.financial-form-item-label-required::before {
  content: '*';
  color: var(--yy-danger);
  margin-right: 4px;
}

.financial-form-item-help {
  margin-top: 4px;
  color: var(--yy-text-light);
  font-size: var(--yy-font-size-sm);
}

.financial-form-item-error {
  margin-top: 4px;
  color: var(--yy-danger);
  font-size: var(--yy-font-size-sm);
}

/* 新增：输入框样式 */
.financial-input {
  display: inline-flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-input-disabled {
  background: var(--yy-table-header);
  cursor: not-allowed;
  color: var(--yy-text-light);
}

.financial-input-error {
  border-color: var(--yy-danger);
}

.financial-input-error:focus {
  box-shadow: 0 0 0 2px rgba(245,34,45,0.2);
}

.financial-input-prefix,
.financial-input-suffix {
  display: flex;
  align-items: center;
  color: var(--yy-text-light);
}

.financial-input-prefix {
  margin-right: 8px;
}

.financial-input-suffix {
  margin-left: 8px;
}

/* 新增：文本域样式 */
.financial-textarea {
  width: 100%;
  min-height: 80px;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  resize: vertical;
}

.financial-textarea:hover {
  border-color: var(--yy-primary-hover);
}

.financial-textarea:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

/* 新增：选择器样式 */
.financial-select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-select-selector {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
  cursor: pointer;
}

.financial-select-selector:hover {
  border-color: var(--yy-primary-hover);
}

.financial-select-selector:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--yy-text-light);
  transition: all 0.3s;
}

.financial-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  padding: 4px 0;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

.financial-select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.financial-select-option:hover {
  background: var(--yy-table-header);
}

.financial-select-option-selected {
  background: #e6f7ff;
  color: var(--yy-primary);
}

/* 新增：日期选择器样式 */
.financial-datepicker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.financial-datepicker-input {
  width: 100%;
  padding: 8px 12px;
  font-size: var(--yy-font-size-base);
  line-height: var(--yy-line-height);
  color: var(--yy-text);
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-datepicker-input:hover {
  border-color: var(--yy-primary-hover);
}

.financial-datepicker-input:focus {
  border-color: var(--yy-primary);
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
  outline: none;
}

.financial-datepicker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  margin-top: 4px;
  padding: 12px;
  background: #fff;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  box-shadow: var(--yy-shadow);
  z-index: 1000;
}

/* 新增：单选框样式 */
.financial-radio {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-radio-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-radio-input:checked + .financial-radio-inner {
  border-color: var(--yy-primary);
}

.financial-radio-input:checked + .financial-radio-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--yy-primary);
  border-radius: 50%;
}

/* 新增：复选框样式 */
.financial-checkbox {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
  cursor: pointer;
}

.financial-checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-checkbox-inner {
  position: relative;
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-checkbox-input:checked + .financial-checkbox-inner {
  background: var(--yy-primary);
  border-color: var(--yy-primary);
}

.financial-checkbox-input:checked + .financial-checkbox-inner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
  width: 4px;
  height: 8px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
}

/* 新增：开关样式 */
.financial-switch {
  position: relative;
  display: inline-block;
  min-width: 44px;
  height: 22px;
  cursor: pointer;
}

.financial-switch-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.financial-switch-inner {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  background: var(--yy-border);
  border-radius: 11px;
  transition: all 0.3s;
}

.financial-switch-inner::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: #fff;
  border-radius: 50%;
  transition: all 0.3s;
}

.financial-switch-input:checked + .financial-switch-inner {
  background: var(--yy-primary);
}

.financial-switch-input:checked + .financial-switch-inner::after {
  left: calc(100% - 20px);
}

/* 新增：上传组件样式 */
.financial-upload {
  display: inline-block;
  cursor: pointer;
}

.financial-upload-input {
  display: none;
}

.financial-upload-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: var(--yy-font-size-base);
  color: var(--yy-text);
  background: #fff;
  border: 1px dashed var(--yy-border);
  border-radius: var(--yy-radius);
  transition: all 0.3s;
}

.financial-upload-trigger:hover {
  border-color: var(--yy-primary);
  color: var(--yy-primary);
}

.financial-upload-list {
  margin-top: 8px;
}

.financial-upload-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid var(--yy-border);
  border-radius: var(--yy-radius);
  margin-bottom: 8px;
}

.financial-upload-item-name {
  flex: 1;
  margin: 0 8px;
  color: var(--yy-text);
}

.financial-upload-item-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.financial-upload-item-action {
  color: var(--yy-text-light);
  cursor: pointer;
  transition: all 0.3s;
}

.financial-upload-item-action:hover {
  color: var(--yy-primary);
}