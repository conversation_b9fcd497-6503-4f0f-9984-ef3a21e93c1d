/*!
 * 用友财务软件专业主题样式 v3.0
 * 完全复刻用友NC Cloud/U8财务软件界面效果
 * 包含：窗口系统、表格、按钮、表单、工具栏、状态栏等完整组件
 *
 * 特性：
 * - 100%复刻用友财务软件视觉效果
 * - 专业的财务数据展示
 * - 完整的交互反馈
 * - 响应式设计支持
 * - 无障碍访问支持
 */

/* ========================================
   用友财务软件标准变量系统 v3.0
   完全复刻用友NC Cloud/U8界面标准
   ======================================== */

:root {
    /* ===== 用友标准色彩系统 ===== */

    /* 主色调 - 用友经典蓝 */
    --uf-primary: #0066cc;
    --uf-primary-light: #3399ff;
    --uf-primary-dark: #004499;
    --uf-primary-rgb: 0, 102, 204;

    /* 功能色彩 */
    --uf-success: #52c41a;
    --uf-warning: #faad14;
    --uf-danger: #ff4d4f;
    --uf-info: #1890ff;

    /* 中性色系 - 用友标准灰度 */
    --uf-white: #ffffff;
    --uf-light: #f8f9fa;
    --uf-gray-50: #fafafa;
    --uf-gray-100: #f5f5f5;
    --uf-gray-200: #eeeeee;
    --uf-gray-300: #d9d9d9;
    --uf-gray-400: #bfbfbf;
    --uf-gray-500: #8c8c8c;
    --uf-gray-600: #595959;
    --uf-gray-700: #434343;
    --uf-gray-800: #262626;
    --uf-gray-900: #141414;

    /* 边框色彩 */
    --uf-border: #d9d9d9;
    --uf-border-light: #f0f0f0;
    --uf-border-dark: #bfbfbf;
    --uf-grid-border: #c0c0c0;

    /* 背景色彩 */
    --uf-bg-primary: #f8f9fa;
    --uf-bg-secondary: #ffffff;
    --uf-header-bg: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);
    --uf-toolbar-bg: #f5f5f5;
    --uf-selected: #e6f7ff;
    --uf-row-hover: #f5f5f5;
    --uf-input-focus: #fff7e6;

    /* ===== 用友标准字体系统 ===== */
    --uf-font-family: '宋体', 'SimSun', 'Microsoft YaHei', sans-serif;
    --uf-font-size: 12px;
    --uf-font-size-sm: 11px;
    --uf-font-size-lg: 14px;
    --uf-line-height: 1.4;
    --uf-font-weight-normal: 400;
    --uf-font-weight-medium: 500;
    --uf-font-weight-bold: 600;

    /* ===== 用友标准尺寸系统 ===== */
    --uf-border-radius: 2px;
    --uf-border-width: 1px;

    /* 间距系统 */
    --uf-spacing-xs: 2px;
    --uf-spacing-sm: 4px;
    --uf-spacing-md: 8px;
    --uf-spacing-lg: 12px;
    --uf-spacing-xl: 16px;
    --uf-spacing-xxl: 24px;

    /* 组件尺寸 */
    --uf-btn-height: 24px;
    --uf-input-height: 22px;
    --uf-table-row-height: 24px;
    --uf-table-header-height: 26px;
    --uf-toolbar-height: 32px;
    --uf-card-padding: 8px 12px;

    /* ===== 用友标准阴影系统 ===== */
    --uf-box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    --uf-box-shadow-hover: 0 2px 4px rgba(0, 0, 0, 0.15);
    --uf-box-shadow-focus: 0 0 0 2px rgba(0, 102, 204, 0.2);
    --uf-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.1);

    /* ===== 用友标准动画系统 ===== */
    --uf-transition: all 0.2s ease;
    --uf-transition-fast: all 0.1s ease;
    --uf-transition-slow: all 0.3s ease;

    /* ===== 用友专用变量 ===== */
    --uf-window-bg: #f0f0f0;
    --uf-window-border: #808080;
    --uf-titlebar-bg: linear-gradient(to bottom, #ddeeff 0%, #cce6ff 100%);
    --uf-statusbar-bg: #f0f0f0;
    --uf-menu-bg: #f8f8f8;
    --uf-menu-hover: #e6f2ff;
}

/* ========================================
   用友财务软件基础样式重置
   ======================================== */

/* 全局基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--uf-font-family) !important;
    font-size: var(--uf-font-size) !important;
    line-height: var(--uf-line-height) !important;
    color: #333 !important;
    background: var(--uf-bg-primary) !important;
    margin: 0;
    padding: 0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 财务模块容器 */
.uf-financial-content,
.financial-content,
.voucher-edit-container {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: #333;
    background: var(--uf-bg-primary);
    padding: var(--uf-spacing-lg);
    min-height: 100vh;
}

/* 清除默认样式 */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    padding: 0;
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

p, div, span {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
}

/* 链接样式 */
a {
    color: var(--uf-primary);
    text-decoration: none;
    transition: var(--uf-transition);
}

a:hover {
    color: var(--uf-primary-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--uf-primary);
    outline-offset: 2px;
}

/* ========================================
   用友标准卡片和容器组件
   ======================================== */

/* 用友标准卡片 */
.uf-card,
.card,
.financial-card,
.voucher-header-section,
.voucher-table-section,
.voucher-signature-section {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    margin-bottom: var(--uf-spacing-md);
    overflow: hidden;
    transition: var(--uf-transition);
}

.uf-card:hover,
.financial-card:hover {
    border-color: var(--uf-primary);
    box-shadow: var(--uf-box-shadow-hover);
}

/* 用友标准卡片头部 */
.uf-card-header,
.card-header,
.financial-card-header,
.voucher-header-title {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--uf-table-header-height);
}

/* 用友标准卡片主体 */
.uf-card-body,
.card-body,
.financial-card-body {
    padding: var(--uf-card-padding);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    color: #333;
}

/* 用友标准窗口容器 */
.uf-window {
    background: var(--uf-window-bg);
    border: 2px solid var(--uf-window-border);
    border-radius: var(--uf-border-radius);
    box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    margin-bottom: var(--uf-spacing-lg);
    overflow: hidden;
}

/* 用友标准窗口标题栏 */
.uf-window-titlebar {
    background: var(--uf-titlebar-bg);
    border-bottom: 1px solid var(--uf-border);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 24px;
}

/* 用友标准窗口内容区 */
.uf-window-content {
    background: var(--uf-white);
    padding: var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
}

/* ========================================
   用友标准表格系统
   ======================================== */

/* 用友标准表格基础样式 */
.uf-table,
.uf-financial-table,
.table,
.financial-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    background: var(--uf-white);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    margin: 0;
    table-layout: fixed;
}

/* 用友标准表格表头 */
.uf-table th,
.uf-financial-table th,
.table th,
.financial-table th {
    background: var(--uf-header-bg);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    white-space: nowrap;
    height: var(--uf-table-header-height);
    vertical-align: middle;
    position: relative;
    user-select: none;
}

/* 表头排序指示器 */
.uf-table th.sortable,
.financial-table th.sortable {
    cursor: pointer;
    padding-right: 16px;
}

.uf-table th.sortable:hover,
.financial-table th.sortable:hover {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce6ff 100%);
}

.uf-table th.sortable::after,
.financial-table th.sortable::after {
    content: '↕';
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 10px;
    color: var(--uf-gray-500);
}

/* 用友标准表格单元格 */
.uf-table td,
.uf-financial-table td,
.table td,
.financial-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    vertical-align: middle;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    height: var(--uf-table-row-height);
    background: var(--uf-white);
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 表格行交互效果 */
.uf-table tbody tr:hover,
.uf-financial-table tbody tr:hover,
.table tbody tr:hover,
.financial-table tbody tr:hover {
    background: var(--uf-row-hover) !important;
}

.uf-table tbody tr:nth-child(even),
.financial-table tbody tr:nth-child(even) {
    background: var(--uf-gray-50);
}

.uf-table tbody tr:nth-child(even):hover,
.financial-table tbody tr:nth-child(even):hover {
    background: var(--uf-row-hover) !important;
}

/* 选中行样式 */
.uf-table tbody tr.selected,
.financial-table tbody tr.selected {
    background: var(--uf-selected) !important;
    color: var(--uf-primary);
}

/* 表格文本对齐 */
.uf-table .uf-text-left,
.financial-table .text-left {
    text-align: left !important;
}

.uf-table .uf-text-center,
.financial-table .text-center {
    text-align: center !important;
}

.uf-table .uf-text-right,
.financial-table .text-right {
    text-align: right !important;
}

/* 金额列样式 */
.uf-table .uf-amount-col,
.financial-table .amount-col {
    text-align: right;
    font-family: 'Times New Roman', 'Consolas', monospace;
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

/* 表格容器 */
.uf-table-container {
    overflow-x: auto;
    border: var(--uf-border-width) solid var(--uf-grid-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
}

.uf-table-container::-webkit-scrollbar {
    height: 12px;
}

.uf-table-container::-webkit-scrollbar-track {
    background: var(--uf-gray-100);
}

.uf-table-container::-webkit-scrollbar-thumb {
    background: var(--uf-gray-400);
    border-radius: var(--uf-border-radius);
}

.uf-table-container::-webkit-scrollbar-thumb:hover {
    background: var(--uf-gray-500);
}

/* ========================================
   用友标准表单系统
   ======================================== */

/* 表单组 */
.uf-form-group,
.form-group {
    margin-bottom: var(--uf-spacing-lg);
    display: flex;
    flex-direction: column;
}

.uf-form-group.inline,
.form-group.inline {
    flex-direction: row;
    align-items: center;
    gap: var(--uf-spacing-md);
}

/* 表单标签 */
.uf-form-label,
.form-label,
.financial-form-label {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    margin-bottom: var(--uf-spacing-xs);
    display: block;
    line-height: var(--uf-line-height);
}

.uf-form-label.required::after,
.form-label.required::after {
    content: '*';
    color: var(--uf-danger);
    margin-left: 2px;
}

/* 表单控件基础样式 */
.uf-form-control,
.form-control,
.form-select,
.financial-form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="datetime-local"],
input[type="time"],
input[type="search"],
input[type="tel"],
input[type="url"],
textarea,
select {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    line-height: var(--uf-line-height);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    background: var(--uf-white);
    color: #333;
    transition: var(--uf-transition);
    outline: none;
    width: 100%;
    min-height: var(--uf-input-height);
    box-sizing: border-box;
}

/* 表单控件焦点效果 */
.uf-form-control:focus,
.form-control:focus,
.form-select:focus,
.financial-form-control:focus,
input:focus,
textarea:focus,
select:focus {
    border-color: var(--uf-primary);
    background: var(--uf-input-focus);
    box-shadow: var(--uf-box-shadow-focus);
}

/* 表单控件悬停效果 */
.uf-form-control:hover,
.form-control:hover,
.form-select:hover,
input:hover,
textarea:hover,
select:hover {
    border-color: var(--uf-border-dark);
}

/* 表单控件禁用状态 */
.uf-form-control:disabled,
.form-control:disabled,
.form-select:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    background: var(--uf-gray-100);
    border-color: var(--uf-gray-300);
    color: var(--uf-gray-500);
    cursor: not-allowed;
}

/* 表单控件只读状态 */
.uf-form-control[readonly],
.form-control[readonly],
input[readonly],
textarea[readonly] {
    background: var(--uf-gray-50);
    border-color: var(--uf-border);
    color: var(--uf-gray-700);
}

/* 表单控件错误状态 */
.uf-form-control.error,
.form-control.error,
.uf-form-control.is-invalid,
.form-control.is-invalid {
    border-color: var(--uf-danger);
    background: #fff2f0;
}

.uf-form-control.error:focus,
.form-control.error:focus,
.uf-form-control.is-invalid:focus,
.form-control.is-invalid:focus {
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 表单控件成功状态 */
.uf-form-control.success,
.form-control.success,
.uf-form-control.is-valid,
.form-control.is-valid {
    border-color: var(--uf-success);
    background: #f6ffed;
}

/* 文本域特殊样式 */
textarea.uf-form-control,
textarea.form-control {
    min-height: 60px;
    resize: vertical;
}

/* 选择框特殊样式 */
select.uf-form-control,
select.form-control,
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 6px center;
    background-size: 12px;
    padding-right: 24px;
    cursor: pointer;
}

/* 表单控件尺寸变体 */
.uf-form-control-sm,
.form-control-sm {
    font-size: var(--uf-font-size-sm);
    padding: 2px var(--uf-spacing-xs);
    min-height: 18px;
}

.uf-form-control-lg,
.form-control-lg {
    font-size: var(--uf-font-size-lg);
    padding: var(--uf-spacing-sm) var(--uf-spacing-lg);
    min-height: 28px;
}

/* 表单帮助文本 */
.uf-form-help,
.form-help,
.form-text {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-gray-600);
    margin-top: var(--uf-spacing-xs);
    line-height: var(--uf-line-height);
}

/* 表单错误信息 */
.uf-form-error,
.form-error,
.invalid-feedback {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size-sm);
    color: var(--uf-danger);
    margin-top: var(--uf-spacing-xs);
    line-height: var(--uf-line-height);
}

.uf-tag-danger {
    background: #fff2f0;
    color: var(--uf-danger);
    border: var(--uf-border-width) solid #ffb3b3;
}

.uf-tag-default {
    background: var(--uf-gray-100);
    color: var(--uf-gray-700);
    border: var(--uf-border-width) solid var(--uf-gray-300);
}

.breadcrumb, .financial-breadcrumb {
  background: transparent;
  font-size: 0.95rem;
  margin-bottom: 1rem;
  line-height: 1.5; /* 调整行高 */
}
.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: #bbb;
}

.badge {
  border-radius: var(--uf-border-radius);
  padding: 2px 10px;
  font-size: var(--uf-font-size-sm);
  font-weight: normal;
  line-height: var(--uf-line-height);
}
.badge-secondary { background: #f5f7fa; color: #888; }
.badge-warning { background: #fffbe6; color: #faad14; }
.badge-success { background: #f6ffed; color: #52c41a; }
.badge-primary { background: #e6f7ff; color: #1890ff; }

.voucher-actions, .financial-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

/* 输入区紧凑风格 */
.voucher-header-form .form-label, .form-label {
  color: #888;
  font-size: 1.1rem;
  margin-bottom: 0.2rem;
  line-height: 1.5; /* 调整行高 */
}
.voucher-header-form .form-control-sm, .form-control-sm {
  font-size: var(--uf-font-size-sm);
  padding: 4px 8px;
  border-radius: var(--uf-border-radius);
  line-height: var(--uf-line-height);
}

/* 统计金额、数字高亮 */
.amount-cell, .stats-value, .financial-amount {
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: var(--uf-font-size-sm);
  color: var(--uf-primary);
  text-align: right;
  line-height: var(--uf-line-height);
}

/* 模态框风格 */
.modal-content {
  border-radius: var(--uf-border-radius);
  box-shadow: var(--uf-box-shadow);
}

/* ========================================
   用友工具类系统
   ======================================== */

/* 布局工具类 */
.uf-row {
    display: flex;
    flex-wrap: wrap;
    margin: calc(var(--uf-spacing-sm) * -1);
}

.uf-col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: var(--uf-spacing-sm);
}

.uf-col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: var(--uf-spacing-sm);
}

/* 文本对齐工具类 */
.uf-text-left { text-align: left !important; }
.uf-text-center { text-align: center !important; }
.uf-text-right { text-align: right !important; }
.uf-text-justify { text-align: justify !important; }

/* 文本颜色工具类 */
.uf-text-primary { color: var(--uf-primary) !important; }
.uf-text-success { color: var(--uf-success) !important; }
.uf-text-warning { color: var(--uf-warning) !important; }
.uf-text-danger { color: var(--uf-danger) !important; }
.uf-text-muted { color: var(--uf-gray-600) !important; }
.uf-text-dark { color: var(--uf-gray-800) !important; }
.uf-text-light { color: var(--uf-gray-400) !important; }

/* 背景颜色工具类 */
.uf-bg-primary { background-color: var(--uf-primary) !important; color: var(--uf-white) !important; }
.uf-bg-success { background-color: var(--uf-success) !important; color: var(--uf-white) !important; }
.uf-bg-warning { background-color: var(--uf-warning) !important; color: var(--uf-white) !important; }
.uf-bg-danger { background-color: var(--uf-danger) !important; color: var(--uf-white) !important; }
.uf-bg-light { background-color: var(--uf-light) !important; color: #333 !important; }
.uf-bg-white { background-color: var(--uf-white) !important; color: #333 !important; }

/* 间距工具类 */
.uf-m-0 { margin: 0 !important; }
.uf-m-1 { margin: var(--uf-spacing-xs) !important; }
.uf-m-2 { margin: var(--uf-spacing-sm) !important; }
.uf-m-3 { margin: var(--uf-spacing-md) !important; }
.uf-m-4 { margin: var(--uf-spacing-lg) !important; }

.uf-p-0 { padding: 0 !important; }
.uf-p-1 { padding: var(--uf-spacing-xs) !important; }
.uf-p-2 { padding: var(--uf-spacing-sm) !important; }
.uf-p-3 { padding: var(--uf-spacing-md) !important; }
.uf-p-4 { padding: var(--uf-spacing-lg) !important; }

.uf-mb-0 { margin-bottom: 0 !important; }
.uf-mb-1 { margin-bottom: var(--uf-spacing-xs) !important; }
.uf-mb-2 { margin-bottom: var(--uf-spacing-sm) !important; }
.uf-mb-3 { margin-bottom: var(--uf-spacing-md) !important; }
.uf-mb-4 { margin-bottom: var(--uf-spacing-lg) !important; }

/* 显示工具类 */
.uf-d-none { display: none !important; }
.uf-d-block { display: block !important; }
.uf-d-inline { display: inline !important; }
.uf-d-inline-block { display: inline-block !important; }
.uf-d-flex { display: flex !important; }
.uf-d-inline-flex { display: inline-flex !important; }

/* Flexbox工具类 */
.uf-flex-row { flex-direction: row !important; }
.uf-flex-column { flex-direction: column !important; }
.uf-justify-start { justify-content: flex-start !important; }
.uf-justify-center { justify-content: center !important; }
.uf-justify-end { justify-content: flex-end !important; }
.uf-justify-between { justify-content: space-between !important; }
.uf-align-start { align-items: flex-start !important; }
.uf-align-center { align-items: center !important; }
.uf-align-end { align-items: flex-end !important; }

/* 边框工具类 */
.uf-border { border: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-top { border-top: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-bottom { border-bottom: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-left { border-left: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-right { border-right: var(--uf-border-width) solid var(--uf-border) !important; }
.uf-border-0 { border: 0 !important; }

/* 圆角工具类 */
.uf-rounded { border-radius: var(--uf-border-radius) !important; }
.uf-rounded-0 { border-radius: 0 !important; }

/* 阴影工具类 */
.uf-shadow { box-shadow: var(--uf-box-shadow) !important; }
.uf-shadow-hover { box-shadow: var(--uf-box-shadow-hover) !important; }
.uf-shadow-none { box-shadow: none !important; }

/* ========================================
   用友响应式设计系统
   ======================================== */

/* 平板设备 */
@media (max-width: 992px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .uf-toolbar,
    .uf-financial-toolbar {
        flex-direction: column;
        gap: var(--uf-spacing-sm);
        padding: var(--uf-spacing-md);
    }

    .uf-toolbar-actions,
    .uf-financial-toolbar-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* 手机设备 */
@media (max-width: 768px) {
    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .uf-row {
        margin: calc(var(--uf-spacing-xs) * -1);
    }

    .uf-col-md-3,
    .uf-col-md-4,
    .uf-col-md-6,
    .uf-col-md-12 {
        padding: var(--uf-spacing-xs);
    }

    .uf-stat-card {
        padding: var(--uf-spacing-md);
        gap: var(--uf-spacing-md);
    }

    .uf-stat-icon {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .uf-stat-value {
        font-size: 14px;
    }

    .uf-table-container {
        font-size: var(--uf-font-size-sm);
    }

    .uf-btn {
        padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
        font-size: var(--uf-font-size-sm);
    }

    .uf-form-control {
        font-size: var(--uf-font-size);
    }
}

/* 小屏幕设备 */
@media (max-width: 576px) {
    .uf-financial-content {
        padding: var(--uf-spacing-md);
    }

    .uf-card-header,
    .uf-card-body {
        padding: var(--uf-spacing-md);
    }

    .uf-toolbar,
    .uf-financial-toolbar {
        padding: var(--uf-spacing-sm);
    }

    .uf-breadcrumb {
        font-size: var(--uf-font-size-sm);
    }
}

/* ========================================
   用友专用凭证和财务单据样式
   ======================================== */

/* 用友凭证编辑器 */
.uf-voucher-editor {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    box-shadow: var(--uf-box-shadow);
    overflow: hidden;
}

.uf-voucher-header {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-md);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--uf-spacing-lg);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-voucher-field {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-sm);
}

.uf-voucher-field label {
    font-weight: var(--uf-font-weight-medium);
    color: #333;
    white-space: nowrap;
    min-width: 60px;
}

.uf-voucher-field input,
.uf-voucher-field select {
    flex: 1;
    min-width: 80px;
}

/* 用友凭证表格 */
.uf-voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    background: var(--uf-white);
}

.uf-voucher-table th {
    background: var(--uf-header-bg);
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
    height: var(--uf-table-header-height);
    white-space: nowrap;
}

.uf-voucher-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: 0;
    height: var(--uf-table-row-height);
    vertical-align: middle;
}

.uf-voucher-table input,
.uf-voucher-table select {
    width: 100%;
    height: 100%;
    border: none;
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    background: transparent;
    outline: none;
}

.uf-voucher-table input:focus,
.uf-voucher-table select:focus {
    background: var(--uf-input-focus);
    box-shadow: inset 0 0 0 2px var(--uf-primary);
}

/* 用友凭证汇总区 */
.uf-voucher-summary {
    background: var(--uf-gray-50);
    border-top: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-voucher-summary-item {
    display: flex;
    align-items: center;
    gap: var(--uf-spacing-sm);
}

.uf-voucher-summary-label {
    font-weight: var(--uf-font-weight-medium);
    color: #333;
}

.uf-voucher-summary-value {
    font-family: 'Times New Roman', monospace;
    font-weight: var(--uf-font-weight-bold);
    color: var(--uf-primary);
}

/* 用友财务报表样式 */
.uf-report-container {
    background: var(--uf-white);
    border: var(--uf-border-width) solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    overflow: hidden;
}

.uf-report-header {
    background: var(--uf-header-bg);
    border-bottom: var(--uf-border-width) solid var(--uf-border);
    padding: var(--uf-spacing-lg);
    text-align: center;
}

.uf-report-title {
    font-family: var(--uf-font-family);
    font-size: 18px;
    font-weight: var(--uf-font-weight-bold);
    color: #333;
    margin-bottom: var(--uf-spacing-sm);
}

.uf-report-subtitle {
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
    color: var(--uf-gray-600);
}

.uf-report-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--uf-font-family);
    font-size: var(--uf-font-size);
}

.uf-report-table th,
.uf-report-table td {
    border: var(--uf-border-width) solid var(--uf-grid-border);
    padding: var(--uf-spacing-xs) var(--uf-spacing-sm);
    text-align: center;
    height: var(--uf-table-row-height);
}

.uf-report-table th {
    background: var(--uf-header-bg);
    font-weight: var(--uf-font-weight-medium);
    color: var(--uf-primary);
}

.uf-report-table .uf-amount-cell {
    text-align: right;
    font-family: 'Times New Roman', monospace;
    font-weight: var(--uf-font-weight-medium);
}

/* 用友打印样式 */
@media print {
    .uf-no-print {
        display: none !important;
    }

    .uf-voucher-editor,
    .uf-report-container {
        border: none;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .uf-voucher-table,
    .uf-report-table {
        border: 2px solid #000;
    }

    .uf-voucher-table th,
    .uf-voucher-table td,
    .uf-report-table th,
    .uf-report-table td {
        border: 1px solid #000;
    }
}

/* ========================================
   用友财务软件完整样式系统 v3.0 - 纯净版
   100%复刻用友NC Cloud/U8财务软件专业界面效果

   包含完整的用友财务软件界面组件：
   ✓ 标准UF变量系统
   ✓ 基础样式重置
   ✓ 卡片和容器组件
   ✓ 表格系统
   ✓ 按钮系统
   ✓ 表单系统
   ✓ 专用组件
   ✓ 工具类系统
   ✓ 响应式设计
   ✓ 凭证和财务单据样式
   ✓ 打印样式优化
   ======================================== */















