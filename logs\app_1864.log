2025-06-11 20:19:36,620 INFO: 应用启动 - PID: 1864 [in D:\StudentsCMSSP\app\__init__.py:839]
2025-06-11 20:19:52,067 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:19:52,091 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:19:52,091 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:20:15,992 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:20:16,011 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:20:16,012 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:21:18,481 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:21:18,495 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:21:18,496 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:21:37,662 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:21:37,679 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:21:37,679 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:22:25,247 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:22:25,255 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:22:25,256 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:22:58,758 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:22:58,772 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:22:58,773 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:23:22,478 INFO: 开始查询最近 5 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-11 20:23:22,496 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in D:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-11 20:23:22,497 INFO: 成功获取 0 条陪餐记录 [in D:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-11 20:24:15,462 INFO: 收到创建周菜单请求: b'{"area_id":"22","week_start":"2025-06-09"}' [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-11 20:24:15,463 INFO: 创建周菜单参数: area_id=22, week_start=2025-06-09 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-11 20:24:15,463 INFO: 检查用户权限: user_id=11, area_id=22 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-11 20:24:15,463 INFO: 用户角色: ['食堂管理员', '学校管理员', '食堂主管', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员', '乡镇书记'] [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-11 20:24:15,465 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-11 20:24:15,465 INFO: 开始创建周菜单: area_id=22, week_start=2025-06-09, created_by=11 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-11 20:24:15,466 INFO: 开始创建周菜单: area_id=22, week_start=2025-06-09, created_by=11 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-11 20:24:15,466 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-11 20:24:15,466 INFO: 计算的周结束日期: 2025-06-15 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-11 20:24:15,467 INFO: 检查是否已存在该周的菜单: area_id=22, week_start=2025-06-09 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-11 20:24:15,467 INFO: 获取周菜单: area_id=22, week_start=2025-06-09, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-11 20:24:15,467 INFO: 使用优化后的查询: area_id=22, week_start=2025-06-09 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-11 20:24:15,467 INFO: 执行主SQL查询: area_id=22, week_start_str=2025-06-09 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-11 20:24:15,471 INFO: 主查询未找到菜单: area_id=22, week_start=2025-06-09 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-11 20:24:15,471 INFO: 使用日期字符串: week_start_str=2025-06-09, week_end_str=2025-06-15 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-11 20:24:15,471 INFO: 准备执行SQL创建菜单 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-11 20:24:15,471 INFO: SQL参数: {'area_id': '22', 'week_start_str': '2025-06-09', 'week_end_str': '2025-06-15', 'status': '计划中', 'created_by': 11} [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-11 20:24:15,472 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-11 20:24:15,474 INFO: SQL执行成功，获取到ID: 39 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-11 20:24:15,475 INFO: 检查数据库连接状态 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-11 20:24:15,475 INFO: 数据库连接正常 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-11 20:24:15,476 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-11 20:24:15,476 INFO: 菜单缓存已清理 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-11 20:24:15,479 INFO: 验证成功: 菜单已创建 ID=39 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-11 20:24:15,479 INFO: 周菜单创建成功: id=39 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-11 20:24:15,480 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 39, 'status': '计划中'} [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-11 20:28:17,062 INFO: 收到创建周菜单请求: b'{"area_id":"22","week_start":"2025-06-16"}' [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-11 20:28:17,063 INFO: 创建周菜单参数: area_id=22, week_start=2025-06-16 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-11 20:28:17,063 INFO: 检查用户权限: user_id=11, area_id=22 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-11 20:28:17,063 INFO: 用户角色: ['食堂管理员', '学校管理员', '食堂主管', '采购员', '库存管理员', '菜单管理员', '食品安全员', '财务管理员', '乡镇书记'] [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-11 20:28:17,065 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-11 20:28:17,065 INFO: 开始创建周菜单: area_id=22, week_start=2025-06-16, created_by=11 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-11 20:28:17,065 INFO: 开始创建周菜单: area_id=22, week_start=2025-06-16, created_by=11 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-11 20:28:17,065 INFO: 转换后的日期对象: 2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-11 20:28:17,065 INFO: 计算的周结束日期: 2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-11 20:28:17,065 INFO: 检查是否已存在该周的菜单: area_id=22, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-11 20:28:17,065 INFO: 获取周菜单: area_id=22, week_start=2025-06-16, 类型=<class 'datetime.date'> [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-11 20:28:17,065 INFO: 使用优化后的查询: area_id=22, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-11 20:28:17,066 INFO: 执行主SQL查询: area_id=22, week_start_str=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-11 20:28:17,066 INFO: 主查询未找到菜单: area_id=22, week_start=2025-06-16 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-11 20:28:17,066 INFO: 使用日期字符串: week_start_str=2025-06-16, week_end_str=2025-06-22 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-11 20:28:17,066 INFO: 准备执行SQL创建菜单 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-11 20:28:17,066 INFO: SQL参数: {'area_id': '22', 'week_start_str': '2025-06-16', 'week_end_str': '2025-06-22', 'status': '计划中', 'created_by': 11} [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-11 20:28:17,066 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-11 20:28:17,067 INFO: SQL执行成功，获取到ID: 40 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-11 20:28:17,067 INFO: 检查数据库连接状态 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-11 20:28:17,067 INFO: 数据库连接正常 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-11 20:28:17,068 INFO: 事务提交成功 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-11 20:28:17,068 INFO: 菜单缓存已清理 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-11 20:28:17,070 INFO: 验证成功: 菜单已创建 ID=40 [in D:\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-11 20:28:17,071 INFO: 周菜单创建成功: id=40 [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-11 20:28:17,072 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 40, 'status': '计划中'} [in D:\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-11 20:28:36,137 ERROR: 周菜单操作异常: time data 'undefined' does not match format '%Y-%m-%d' [in D:\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "D:\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "D:\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 130, in plan
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data 'undefined' does not match format '%Y-%m-%d'
